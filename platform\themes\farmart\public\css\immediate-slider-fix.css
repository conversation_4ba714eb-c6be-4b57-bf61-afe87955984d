/* IMMEDIATE SLIDER FIX - Load this first to prevent layout issues */

/* Force horizontal layout for all uninitialized sliders */
.slick-slides-carousel:not(.slick-initialized) {
    display: block !important;
    overflow: hidden !important;
    white-space: nowrap !important;
    height: auto !important;
    position: relative !important;
}

.slick-slides-carousel:not(.slick-initialized) > * {
    display: inline-block !important;
    width: 100% !important;
    vertical-align: top !important;
    white-space: normal !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    opacity: 0 !important;
    visibility: hidden !important;
}

.slick-slides-carousel:not(.slick-initialized) > *:first-child {
    position: relative !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Product gallery immediate fixes */
.bb-product-gallery-images:not(.slick-initialized) {
    display: block !important;
    overflow: hidden !important;
    position: relative !important;
    height: auto !important;
}

.bb-product-gallery-images:not(.slick-initialized) > * {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    opacity: 0 !important;
    visibility: hidden !important;
}

.bb-product-gallery-images:not(.slick-initialized) > *:first-child {
    position: relative !important;
    opacity: 1 !important;
    visibility: visible !important;
}

.bb-product-gallery-thumbnails:not(.slick-initialized) {
    display: flex !important;
    flex-wrap: nowrap !important;
    overflow-x: auto !important;
    gap: 5px !important;
}

.bb-product-gallery-thumbnails:not(.slick-initialized) > * {
    flex: 0 0 auto !important;
    max-width: 80px !important;
}

/* Main slider specific fixes */
.section-content__slider .slick-slides-carousel:not(.slick-initialized) {
    height: 384px !important;
    max-height: 384px !important;
}

/* Product sliders */
.product-deals-day-body.slick-slides-carousel:not(.slick-initialized) > *:not(:first-child),
.products-carousel.slick-slides-carousel:not(.slick-initialized) > *:not(:first-child) {
    display: none !important;
}

/* Hide all but first slide in any uninitialized slider */
.slick-slides-carousel:not(.slick-initialized) .slide-item:not(:first-child),
.slick-slides-carousel:not(.slick-initialized) .product-item:not(:first-child),
.slick-slides-carousel:not(.slick-initialized) > div:not(:first-child) {
    display: none !important;
}

/* Responsive adjustments */
@media (max-width: 767px) {
    .section-content__slider .slick-slides-carousel:not(.slick-initialized) {
        height: 45vw !important;
        max-height: none !important;
    }
}

/* Ensure images don't break layout */
.slick-slides-carousel:not(.slick-initialized) img {
    max-width: 100% !important;
    height: auto !important;
    display: block !important;
}

/* Quick view gallery */
.bb-quick-view-gallery-images:not(.slick-initialized) {
    display: block !important;
    overflow: hidden !important;
}

.bb-quick-view-gallery-images:not(.slick-initialized) > *:not(:first-child) {
    display: none !important;
}
