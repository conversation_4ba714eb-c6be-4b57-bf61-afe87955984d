<?php

// Bootstrap Laravel
require __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Botble\Base\Supports\Helper;
use Botble\PluginManagement\Services\PluginService;

// Get the plugin service
$pluginService = app(PluginService::class);

echo "Activating NagorikPay plugin...\n";

// Activate the plugin
$result = $pluginService->activate('nagorikpay');

if (isset($result['error']) && $result['error']) {
    echo "Failed to activate plugin: " . ($result['message'] ?? 'Unknown error') . "\n";
    exit(1);
} else {
    echo "NagorikPay plugin activated successfully!\n";
}

// Publish assets
echo "Publishing assets...\n";
Helper::executeCommand('php artisan cms:plugin:assets:publish nagorikpay');

echo "Assets published.\n";

// Clear cache
echo "Clearing cache...\n";
Helper::executeCommand('php artisan cache:clear');
Helper::executeCommand('php artisan config:clear');
Helper::executeCommand('php artisan view:clear');

echo "Cache cleared.\n";
echo "NagorikPay plugin is now ready to use!\n";
echo "\nNext steps:\n";
echo "1. Go to Admin Panel > Settings > Payment Methods\n";
echo "2. Configure NagorikPay settings with your API key\n";
echo "3. Enable NagorikPay payment method\n";
echo "4. Test the payment flow\n";
