<?php

echo "=== NagorikPay Plugin Activation ===\n\n";

// Check if plugins.json exists
$pluginsFile = 'storage/app/plugins.json';

if (!file_exists($pluginsFile)) {
    echo "Creating plugins.json file...\n";
    $plugins = [];
} else {
    echo "Reading existing plugins.json...\n";
    $plugins = json_decode(file_get_contents($pluginsFile), true) ?: [];
}

// Activate payment plugin
if (!in_array('payment', $plugins)) {
    $plugins[] = 'payment';
    echo "✅ Added payment plugin to active list\n";
} else {
    echo "✅ Payment plugin already active\n";
}

// Activate nagorikpay plugin
if (!in_array('nagorikpay', $plugins)) {
    $plugins[] = 'nagorikpay';
    echo "✅ Added nagorikpay plugin to active list\n";
} else {
    echo "✅ NagorikPay plugin already active\n";
}

// Save the plugins file
file_put_contents($pluginsFile, json_encode($plugins, JSON_PRETTY_PRINT));
echo "✅ Saved plugins.json\n";

echo "\nActive plugins:\n";
foreach ($plugins as $plugin) {
    echo "  - $plugin\n";
}

echo "\n=== Activation Complete ===\n";
echo "Now try accessing the admin panel:\n";
echo "1. Go to Settings → Payment Methods\n";
echo "2. NagorikPay should now appear in the list\n";
echo "3. If you still get 500 error, check the Laravel logs\n";
