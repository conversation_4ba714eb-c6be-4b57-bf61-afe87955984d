version: '3'
services:
    app:
        image: ghcr.io/archielite/laravel:php8.1
        extra_hosts:
            - 'host.docker.internal:host-gateway'
        ports:
            - '${APP_PORT:-8000}:80'
        volumes:
            - '.:/var/www/html'
        networks:
            - sail
        depends_on:
            - mysql
    mysql:
        image: 'mysql/mysql-server:8.0'
        ports:
            - '4409:3306'
        environment:
            MYSQL_ROOT_PASSWORD: ''
            MYSQL_ROOT_HOST: "%"
            MYSQL_DATABASE: 'naru'
            MYSQL_USER: 'root'
            MYSQL_PASSWORD: ''
            MYSQL_ALLOW_EMPTY_PASSWORD: 1
        volumes:
            - 'sail-mysql:/var/lib/mysql'
        networks:
            - sail
        healthcheck:
            test: ["CMD", "mysqladmin", "ping"]
            retries: 3
            timeout: 5s
    phpmyadmin:
        image: phpmyadmin/phpmyadmin
        container_name: laravel-phpmyadmin
        restart: unless-stopped
        environment:
            PMA_HOST: mysql
            PMA_PORT: 3306
            PMA_USER: root
            PMA_PASSWORD: ''
        ports:
            - "8099:80"
        networks:
            - sail
        depends_on:
            - mysql
networks:
    sail:
        driver: bridge
volumes:
    sail-mysql:
        driver: local
