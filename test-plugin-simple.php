<?php

echo "=== Simple Plugin Test ===\n\n";

// Test 1: Check if plugins.json exists and contains our plugins
$pluginsFile = 'storage/app/plugins.json';
if (file_exists($pluginsFile)) {
    $plugins = json_decode(file_get_contents($pluginsFile), true) ?: [];
    echo "1. Plugin Activation Status:\n";
    echo "   - payment: " . (in_array('payment', $plugins) ? '✅ ACTIVE' : '❌ INACTIVE') . "\n";
    echo "   - nagorikpay: " . (in_array('nagorikpay', $plugins) ? '✅ ACTIVE' : '❌ INACTIVE') . "\n";
} else {
    echo "1. ❌ plugins.json not found\n";
}

// Test 2: Check if plugin files exist
echo "\n2. Plugin Files Check:\n";
$files = [
    'platform/plugins/nagorikpay/plugin.json',
    'platform/plugins/nagorikpay/src/Providers/NagorikPayServiceProvider.php',
    'platform/plugins/nagorikpay/src/Providers/HookServiceProvider.php',
    'platform/plugins/nagorikpay/src/Services/NagorikPayService.php',
    'platform/plugins/nagorikpay/helpers/helpers.php',
    'platform/plugins/nagorikpay/resources/views/methods.blade.php',
    'platform/plugins/nagorikpay/resources/views/settings.blade.php'
];

foreach ($files as $file) {
    $exists = file_exists($file);
    echo "   - " . basename($file) . ": " . ($exists ? '✅ EXISTS' : '❌ MISSING') . "\n";
}

// Test 3: Check constants file
echo "\n3. Constants Check:\n";
$helpersFile = 'platform/plugins/nagorikpay/helpers/helpers.php';
if (file_exists($helpersFile)) {
    $content = file_get_contents($helpersFile);
    if (strpos($content, 'NAGORIKPAY_PAYMENT_METHOD_NAME') !== false) {
        echo "   ✅ Constants defined in helpers.php\n";
    } else {
        echo "   ❌ Constants not found in helpers.php\n";
    }
} else {
    echo "   ❌ helpers.php not found\n";
}

echo "\n=== Test Complete ===\n";
echo "If all tests pass, try accessing the admin panel:\n";
echo "1. Start your web server and database\n";
echo "2. Go to Settings → Payment Methods\n";
echo "3. NagorikPay should now appear in the list\n";
