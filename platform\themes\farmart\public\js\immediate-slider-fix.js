// Immediate slider fix - prevent vertical layout flash
(function() {
    'use strict';
    
    function fixSliderLayout() {
        // Fix main sliders
        var sliders = document.querySelectorAll('.slick-slides-carousel:not(.slick-initialized)');
        sliders.forEach(function(slider) {
            var children = Array.from(slider.children);
            children.forEach(function(child, index) {
                if (index > 0) {
                    child.style.display = 'none';
                    child.style.visibility = 'hidden';
                    child.style.opacity = '0';
                }
            });
        });
        
        // Fix product galleries
        var galleries = document.querySelectorAll('.bb-product-gallery-images:not(.slick-initialized)');
        galleries.forEach(function(gallery) {
            var children = Array.from(gallery.children);
            children.forEach(function(child, index) {
                if (index > 0) {
                    child.style.display = 'none';
                    child.style.visibility = 'hidden';
                    child.style.opacity = '0';
                }
            });
        });
        
        // Fix any other carousel elements
        var carousels = document.querySelectorAll('[class*="carousel"]:not(.slick-initialized), [class*="slider"]:not(.slick-initialized)');
        carousels.forEach(function(carousel) {
            var children = Array.from(carousel.children);
            children.forEach(function(child, index) {
                if (index > 0) {
                    child.style.display = 'none';
                }
            });
        });
    }
    
    // Run immediately
    fixSliderLayout();
    
    // Run when DOM content is loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', fixSliderLayout);
    }
    
    // Run when window loads
    window.addEventListener('load', fixSliderLayout);
    
    // Use MutationObserver to catch dynamically added content
    if (window.MutationObserver) {
        var observer = new MutationObserver(function(mutations) {
            var shouldFix = false;
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    for (var i = 0; i < mutation.addedNodes.length; i++) {
                        var node = mutation.addedNodes[i];
                        if (node.nodeType === 1) { // Element node
                            if (node.classList && (
                                node.classList.contains('slick-slides-carousel') ||
                                node.classList.contains('bb-product-gallery-images') ||
                                node.querySelector('.slick-slides-carousel') ||
                                node.querySelector('.bb-product-gallery-images')
                            )) {
                                shouldFix = true;
                                break;
                            }
                        }
                    }
                }
            });
            
            if (shouldFix) {
                setTimeout(fixSliderLayout, 10);
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    // Periodic check for the first few seconds
    var checkCount = 0;
    var maxChecks = 100; // 5 seconds at 50ms intervals
    var interval = setInterval(function() {
        fixSliderLayout();
        checkCount++;
        
        if (checkCount >= maxChecks) {
            clearInterval(interval);
        }
    }, 50);
    
})();
