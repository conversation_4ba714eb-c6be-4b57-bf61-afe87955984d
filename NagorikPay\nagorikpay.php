<?php


if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

function nagorikpay_MetaData()
{
    return array(
        'DisplayName' => 'nagorikpay',
        'APIVersion' => '1.0',
        'DisableLocalCredtCardInput' => true,
        'TokenisedStorage' => false,
    );
}




function nagorikpay_link($params)
{
    $host_config = $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
    $host_config = pathinfo($host_config, PATHINFO_FILENAME);

    if (isset($_POST['pay'])) {
        $response = nagorikpay_payment_url($params);
        if ($response->status) {
            return '<form action="' . $response->payment_url . ' " method="GET">
            <input class="btn btn-primary" type="submit" value="' . $params['langpaynow'] . '" />
            </form>';
        }

        return $response->message;
    }


    if ($host_config == "viewinvoice") {
        return '<form action="" method="POST">
        <input class="btn btn-primary" name="pay" type="submit" value="' . $params['langpaynow'] . '" />
        </form>';
    } else {
        $response = nagorikpay_payment_url($params);
        if ($response->status) {
            return '<form action="' . $response->payment_url . ' " method="GET">
            <input class="btn btn-primary" type="submit" value="' . $params['langpaynow'] . '" />
            </form>';
        }

        return $response->message;
    }
}


function nagorikpay_config()
{
    return array(
        'FriendlyName' => array(
            'Type' => 'System',
            'Value' => 'nagorikpay',
        ),
        'apiKey' => array(
            'FriendlyName' => 'API Key',
            'Type' => 'text',
            'Size' => '150',
            'Default' => '',
            'Description' => 'Enter Your Api Key',
        ),

        'currency_rate' => array(
            'FriendlyName' => 'Currency Rate',
            'Type' => 'text',
            'Size' => '150',
            'Default' => '85',
            'Description' => 'Enter Dollar Rate',
        )
    );
}

function nagorikpay_payment_url($params)
{
    $cus_name = $params['clientdetails']['firstname'] . " " . $params['clientdetails']['lastname'];
    $cus_email = $params['clientdetails']['email'];

    $apikey = $params['apiKey'];
    $secretkey = $params['secretKey'];

    $currency_rate = $params['currency_rate'];

    $invoiceId = $params['invoiceid'];

    if ($params['currency'] == "USD") {
        $amount = $params['amount'] * $currency_rate;
    } else {
        $amount = $params['amount'];
    }
    $hostname = $params['hostName'];


    if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on')
        $url = "https://";
    else
        $url = "http://";
    // Append the host(domain name, ip) to the URL.   
    $url .= $_SERVER['HTTP_HOST'];

    $systemUrl = $url;

    $webhook_url = $systemUrl . '/modules/gateways/callback/nagorikpay.php?api=' . $apikey . '&invoice=' . $invoiceId;
    $success_url = $systemUrl . '/viewinvoice.php?id=' . $invoiceId;
    $cancel_url = $systemUrl . '/viewinvoice.php?id=' . $invoiceId;


    $data   = array(
        "cus_name"          => $cus_name,
        "cus_email"         => $cus_email,
        "amount"            => $amount,
        "webhook_url"       => $webhook_url,
        "success_url"       => $success_url,
        "cancel_url"        => $cancel_url,
    );

    $header   = array(
        "api"               => $apikey,
        "url"               => 'https://secure-pay.nagorikpay.com/api/payment/create',
    );


    $headers = array(
        'Content-Type: application/json',
        'API-KEY: ' . $header['api'],
    );
    $url = $header['url'];
    $curl = curl_init();
    $data = json_encode($data);

    curl_setopt_array($curl, array(
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => $data,
        CURLOPT_HTTPHEADER => $headers,
        CURLOPT_VERBOSE => true
    ));

    $response = curl_exec($curl);
    curl_close($curl);

    $res = json_decode($response, true);
    if ($res['status'] && $res['payment_url']) {
        header('Location: ' . $res['payment_url']);
        exit();
    } else {
        echo $res['message'];
    }
}