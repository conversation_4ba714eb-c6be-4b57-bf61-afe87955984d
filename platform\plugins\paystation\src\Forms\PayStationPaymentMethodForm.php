<?php

namespace Botble\PayStation\Forms;

use Botble\Base\Forms\FieldOptions\SelectFieldOption;
use Botble\Base\Forms\FieldOptions\TextFieldOption;
use Botble\Base\Forms\Fields\SelectField;
use Botble\Base\Forms\Fields\TextField;
use Bo<PERSON>ble\Payment\Forms\PaymentMethodForm;

class PayStationPaymentMethodForm extends PaymentMethodForm
{
    public function setup(): void
    {
        parent::setup();

        $this
            ->paymentId(PAYSTATION_PAYMENT_METHOD_NAME)
            ->paymentName('PayStation')
            ->paymentDescription('Pay with bKash, Nagad, Rocket, Cards and other payment methods via PayStation')
            ->paymentLogo(url('vendor/core/plugins/paystation/images/paystation.png'))
            ->paymentUrl('https://paystation.com.bd')
            ->paymentInstructions(view('plugins/paystation::instructions')->render())
            ->add(
                'payment_paystation_merchant_id',
                TextField::class,
                TextFieldOption::make()
                    ->label('Merchant ID')
                    ->placeholder('Enter your PayStation Merchant ID')
                    ->value(get_payment_setting('merchant_id', PAYSTATION_PAYMENT_METHOD_NAME, env('PAYSTATION_MERCHANT_ID', '')))
                    ->helperText('Your PayStation Merchant ID (e.g., 204-16537301811)')
                    ->toArray()
            )
            ->add(
                'payment_paystation_password',
                TextField::class,
                TextFieldOption::make()
                    ->label('Password')
                    ->placeholder('Enter your PayStation Password')
                    ->value(get_payment_setting('password', PAYSTATION_PAYMENT_METHOD_NAME, env('PAYSTATION_PASSWORD', '')))
                    ->helperText('Your PayStation Password')
                    ->toArray()
            )
            ->add(
                'payment_paystation_environment',
                SelectField::class,
                SelectFieldOption::make()
                    ->label('Environment')
                    ->choices([
                        'sandbox' => 'Sandbox (Test)',
                        'live' => 'Live (Production)'
                    ])
                    ->value(get_payment_setting('environment', PAYSTATION_PAYMENT_METHOD_NAME, 'sandbox'))
                    ->helperText('Use Sandbox for testing, Live for production')
                    ->toArray()
            )
            ->add(
                'payment_paystation_currency_rate',
                TextField::class,
                TextFieldOption::make()
                    ->label('USD to BDT Rate')
                    ->placeholder('Enter USD to BDT conversion rate')
                    ->attributes(['type' => 'number', 'step' => '0.01'])
                    ->value(get_payment_setting('currency_rate', PAYSTATION_PAYMENT_METHOD_NAME, '110'))
                    ->helperText('Rate to convert USD to BDT (e.g., 110)')
                    ->toArray()
            );
    }
}
