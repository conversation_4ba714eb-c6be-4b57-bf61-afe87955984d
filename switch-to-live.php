<?php

echo "=== PayStation Live Environment Switch ===\n\n";

// You can set your live credentials here
$liveMerchantId = 'your-live-merchant-id';  // Replace with your actual live merchant ID
$livePassword = 'your-live-password';        // Replace with your actual live password

echo "This script will switch PayStation to LIVE environment.\n";
echo "⚠️  WARNING: This will process REAL MONEY transactions!\n\n";

echo "Please update the credentials in this script first:\n";
echo "- Live Merchant ID: $liveMerchantId\n";
echo "- Live Password: $livePassword\n\n";

if ($liveMerchantId === 'your-live-merchant-id' || $livePassword === 'your-live-password') {
    echo "❌ Please update the credentials in this script before running!\n";
    echo "Edit switch-to-live.php and replace the placeholder values.\n";
    exit;
}

echo "Confirm switch to LIVE environment? (y/N): ";
$handle = fopen("php://stdin", "r");
$line = fgets($handle);
fclose($handle);

if (trim($line) !== 'y' && trim($line) !== 'Y') {
    echo "❌ Cancelled. PayStation remains in current environment.\n";
    exit;
}

echo "\n🔄 Switching to LIVE environment...\n";

// Update .env file
$envFile = '.env';
if (file_exists($envFile)) {
    $envContent = file_get_contents($envFile);
    
    // Update or add PayStation environment variables
    $envContent = preg_replace('/^PAYSTATION_ENVIRONMENT=.*/m', 'PAYSTATION_ENVIRONMENT=live', $envContent);
    $envContent = preg_replace('/^PAYSTATION_MERCHANT_ID=.*/m', "PAYSTATION_MERCHANT_ID=$liveMerchantId", $envContent);
    $envContent = preg_replace('/^PAYSTATION_PASSWORD=.*/m', "PAYSTATION_PASSWORD=$livePassword", $envContent);
    
    // If variables don't exist, add them
    if (strpos($envContent, 'PAYSTATION_ENVIRONMENT=') === false) {
        $envContent .= "\n# PayStation Configuration\n";
        $envContent .= "PAYSTATION_ENVIRONMENT=live\n";
        $envContent .= "PAYSTATION_MERCHANT_ID=$liveMerchantId\n";
        $envContent .= "PAYSTATION_PASSWORD=$livePassword\n";
    }
    
    file_put_contents($envFile, $envContent);
    echo "✅ Updated .env file\n";
} else {
    echo "⚠️  .env file not found, please create it manually\n";
}

echo "\n🎯 LIVE Environment Active!\n";
echo "✅ Environment: LIVE\n";
echo "✅ API URL: https://api.paystation.com.bd\n";
echo "✅ Merchant ID: $liveMerchantId\n";
echo "✅ Real money transactions enabled\n\n";

echo "🔧 Next Steps:\n";
echo "1. Go to Admin Panel → Settings → Payment Methods\n";
echo "2. Find PayStation and verify settings\n";
echo "3. Ensure Environment is set to 'Live (Production)'\n";
echo "4. Make a small test payment to verify\n";
echo "5. Monitor your PayStation dashboard\n\n";

echo "⚠️  IMPORTANT REMINDERS:\n";
echo "- All payments will now process REAL MONEY\n";
echo "- Monitor transactions in your PayStation dashboard\n";
echo "- Keep your live credentials secure\n";
echo "- Test thoroughly before announcing to customers\n\n";

echo "🚀 PayStation is now LIVE and ready for real payments!\n";
