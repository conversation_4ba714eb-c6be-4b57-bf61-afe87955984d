<div class="alert alert-info">
    <h5><i class="fa fa-info-circle"></i> PayStation Setup Instructions</h5>
</div>

<ol>
    <li>
        <p><strong>Get PayStation Account</strong></p>
        <p>Contact PayStation to get your merchant account:</p>
        <ul>
            <li><strong>Website:</strong> <a href="https://paystation.com.bd" target="_blank">https://paystation.com.bd</a></li>
            <li><strong>Email:</strong> <EMAIL></li>
        </ul>
    </li>
    
    <li>
        <p><strong>Obtain Credentials</strong></p>
        <p>PayStation will provide you with:</p>
        <ul>
            <li><strong>Merchant ID</strong> - Your unique merchant identifier (e.g., 204-***********)</li>
            <li><strong>Password</strong> - Your merchant password</li>
            <li><strong>Test Credentials</strong> - For sandbox testing</li>
            <li><strong>Live Credentials</strong> - For production use</li>
        </ul>
    </li>
    
    <li>
        <p><strong>Configure Settings</strong></p>
        <ul>
            <li><strong>Merchant ID:</strong> Enter your PayStation Merchant ID</li>
            <li><strong>Password:</strong> Enter your PayStation Password</li>
            <li><strong>Environment:</strong> Choose Sandbox for testing, Live for production</li>
            <li><strong>Currency Rate:</strong> Set USD to BDT conversion rate (current: ~110)</li>
        </ul>
    </li>
    
    <li>
        <p><strong>Environment URLs</strong></p>
        <ul>
            <li><strong>Sandbox:</strong> https://api-sandbox.paystation.com.bd</li>
            <li><strong>Live:</strong> https://api.paystation.com.bd</li>
        </ul>
    </li>
</ol>

<div class="alert alert-warning">
    <h6><i class="fa fa-exclamation-triangle"></i> Important Notes</h6>
    <ul class="mb-0">
        <li>Always test with Sandbox environment first</li>
        <li>Ensure your callback URL is accessible from PayStation servers</li>
        <li>Keep your credentials secure and never share them publicly</li>
        <li>Contact PayStation support for any integration issues</li>
    </ul>
</div>

<div class="mt-4">
    <h6>Current Configuration:</h6>
    <div class="row">
        <div class="col-md-6">
            <table class="table table-sm">
                <tr>
                    <td><strong>Merchant ID:</strong></td>
                    <td>{{ get_payment_setting('merchant_id', PAYSTATION_PAYMENT_METHOD_NAME, 'Not Set') }}</td>
                </tr>
                <tr>
                    <td><strong>Environment:</strong></td>
                    <td>
                        <span class="badge badge-{{ get_payment_setting('environment', PAYSTATION_PAYMENT_METHOD_NAME, 'sandbox') === 'live' ? 'success' : 'warning' }}">
                            {{ ucfirst(get_payment_setting('environment', PAYSTATION_PAYMENT_METHOD_NAME, 'sandbox')) }}
                        </span>
                    </td>
                </tr>
                <tr>
                    <td><strong>Currency Rate:</strong></td>
                    <td>{{ get_payment_setting('currency_rate', PAYSTATION_PAYMENT_METHOD_NAME, '110') }} BDT per USD</td>
                </tr>
            </table>
        </div>
        <div class="col-md-6">
            <h6>Supported Payment Methods:</h6>
            <div class="row">
                <div class="col-6 mb-2">
                    <div class="text-center p-2 border rounded">
                        <strong>bKash</strong>
                    </div>
                </div>
                <div class="col-6 mb-2">
                    <div class="text-center p-2 border rounded">
                        <strong>Nagad</strong>
                    </div>
                </div>
                <div class="col-6 mb-2">
                    <div class="text-center p-2 border rounded">
                        <strong>Rocket</strong>
                    </div>
                </div>
                <div class="col-6 mb-2">
                    <div class="text-center p-2 border rounded">
                        <strong>Cards</strong>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
