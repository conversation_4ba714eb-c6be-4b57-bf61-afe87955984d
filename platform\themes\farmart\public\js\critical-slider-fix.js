// Critical slider fix - runs immediately
(function() {
    'use strict';
    
    function fixSliders() {
        // Get all uninitialized sliders and galleries
        var selectors = [
            '.slick-slides-carousel:not(.slick-initialized)',
            '.bb-product-gallery-images:not(.slick-initialized)',
            '.product-gallery__wrapper:not(.slick-initialized)'
        ];
        
        selectors.forEach(function(selector) {
            var elements = document.querySelectorAll(selector);
            elements.forEach(function(element) {
                var children = element.children;
                // Hide all children except the first one
                for (var i = 1; i < children.length; i++) {
                    children[i].style.display = 'none';
                    children[i].style.visibility = 'hidden';
                    children[i].style.opacity = '0';
                    children[i].style.position = 'absolute';
                    children[i].style.top = '-9999px';
                }
                
                // Ensure first child is visible
                if (children[0]) {
                    children[0].style.display = 'block';
                    children[0].style.visibility = 'visible';
                    children[0].style.opacity = '1';
                    children[0].style.position = 'relative';
                    children[0].style.top = 'auto';
                }
            });
        });
    }
    
    // Run immediately
    fixSliders();
    
    // Run every 10ms for the first second
    var count = 0;
    var interval = setInterval(function() {
        fixSliders();
        count++;
        if (count >= 100) { // 100 * 10ms = 1 second
            clearInterval(interval);
        }
    }, 10);
    
    // Also run on DOM events
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', fixSliders);
    }
    window.addEventListener('load', fixSliders);
    
    // Watch for new elements being added
    if (window.MutationObserver) {
        var observer = new MutationObserver(function(mutations) {
            var needsFix = false;
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) { // Element node
                            if (node.classList && (
                                node.classList.contains('slick-slides-carousel') ||
                                node.classList.contains('bb-product-gallery-images') ||
                                node.querySelector('.slick-slides-carousel') ||
                                node.querySelector('.bb-product-gallery-images')
                            )) {
                                needsFix = true;
                            }
                        }
                    });
                }
            });
            
            if (needsFix) {
                setTimeout(fixSliders, 1);
            }
        });
        
        observer.observe(document.documentElement, {
            childList: true,
            subtree: true
        });
    }
})();
