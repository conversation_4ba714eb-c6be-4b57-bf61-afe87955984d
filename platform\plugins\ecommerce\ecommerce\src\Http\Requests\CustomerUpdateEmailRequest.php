<?php

namespace Bo<PERSON>ble\Ecommerce\Http\Requests;

use Botble\Base\Rules\EmailRule;
use Bo<PERSON>ble\Ecommerce\Models\Customer;
use Bo<PERSON>ble\Support\Http\Requests\Request;
use Illuminate\Validation\Rule;

class CustomerUpdateEmailRequest extends Request
{
    public function rules(): array
    {
        return [
            'email' => ['required', new EmailRule(), Rule::unique((new Customer())->getTable(), 'email')->ignore($this->route('id'))],
        ];
    }
}
