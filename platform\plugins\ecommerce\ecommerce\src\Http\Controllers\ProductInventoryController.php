<?php

namespace Bo<PERSON>ble\Ecommerce\Http\Controllers;

use Bo<PERSON>ble\Base\Facades\Assets;
use Bo<PERSON>ble\Ecommerce\Http\Requests\UpdateProductInventoryRequest;
use Botble\Ecommerce\Models\Product;
use Botble\Ecommerce\Services\Products\UpdateDefaultProductService;
use Bo<PERSON>ble\Ecommerce\Tables\ProductInventoryTable;

class ProductInventoryController extends BaseController
{
    public function index(ProductInventoryTable $dataTable)
    {
        $this->pageTitle(trans('plugins/ecommerce::product-inventory.name'));

        Assets::addScriptsDirectly('vendor/core/plugins/ecommerce/js/product-bulk-editable-table.js');

        return $dataTable->renderTable();
    }

    public function update(Product $product, UpdateProductInventoryRequest $request)
    {
        $product->forceFill([
            $request->input('column') => $request->input('value'),
        ])->save();

        if ($product->is_variation) {
            $product->load('variationInfo.configurableProduct');

            if ($product->variationInfo->is_default) {
                app(UpdateDefaultProductService::class)->execute($product);
            }
        }

        return $this->httpResponse()->withUpdatedSuccessMessage();
    }
}
