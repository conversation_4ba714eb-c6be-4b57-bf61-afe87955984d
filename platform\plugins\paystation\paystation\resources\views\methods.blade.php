@if (get_payment_setting('status', PAYSTATION_PAYMENT_METHOD_NAME) == 1)
    <x-plugins-payment::payment-method
        :name="PAYSTATION_PAYMENT_METHOD_NAME"
        paymentName="PayStation"
        :supportedCurrencies="(new Botble\PayStation\Services\PayStationService)->supportedCurrencyCodes()"
    >
        <div class="paystation-info">
            <div class="paystation-payment-methods mt-3">
                <h6>Choose a Payment Method:</h6>

                <input type="hidden" name="paystation_payment_method" id="paystation_selected_method" value="">

                <div class="row">
                    @php
                        $methods = [
                            'bkash' => 'bKash',
                            'nagad' => 'Nagad',
                            'rocket' => 'Rocket',
                            'upay' => 'Upay',
                            'mastercard' => 'Mastercard',
                            'visa' => 'Visa',
                        ];
                    @endphp

                    @foreach ($methods as $slug => $name)
                        <div class="col-md-2 col-4 mb-2">
                            <div
                                class="payment-method-item p-2 border rounded d-flex align-items-center justify-content-center method-select"
                                data-method="{{ $slug }}"
                                style="height: 60px; cursor: pointer; transition: all 0.3s;"
                            >
                                <img src="{{ asset('images/payment-methods/' . $slug . '.png') }}"
                                     alt="{{ $name }}"
                                     style="max-height: 40px; max-width: 100%; object-fit: contain;">
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>

            <div class="alert alert-info mt-3">
                <small>
                    <i class="fa fa-info-circle"></i>
                    You will be redirected to PayStation secure payment page after clicking "Checkout".
                </small>
            </div>
        </div>

        <script>
            document.addEventListener('DOMContentLoaded', function () {
                const methodItems = document.querySelectorAll('.method-select');
                const hiddenInput = document.getElementById('paystation_selected_method');

                methodItems.forEach(item => {
                    item.addEventListener('click', function () {
                        // Remove active class from all
                        methodItems.forEach(i => i.classList.remove('border-primary', 'shadow'));

                        // Add to clicked one
                        this.classList.add('border-primary', 'shadow');

                        // Set hidden input
                        hiddenInput.value = this.getAttribute('data-method');
                    });
                });
            });
        </script>
    </x-plugins-payment::payment-method>
@endif
