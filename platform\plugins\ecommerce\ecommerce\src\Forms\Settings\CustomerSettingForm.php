<?php

namespace Bo<PERSON><PERSON>\Ecommerce\Forms\Settings;

use Bo<PERSON>ble\Base\Forms\FieldOptions\OnOffFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\RadioFieldOption;
use Bo<PERSON>ble\Base\Forms\Fields\OnOffCheckboxField;
use Bo<PERSON>ble\Base\Forms\Fields\RadioField;
use Bo<PERSON>ble\Ecommerce\Facades\EcommerceHelper;
use Botble\Ecommerce\Http\Requests\Settings\CustomerSettingRequest;
use Bo<PERSON>ble\Setting\Forms\SettingForm;

class CustomerSettingForm extends SettingForm
{
    public function setup(): void
    {
        parent::setup();

        $this
            ->setSectionTitle(trans('plugins/ecommerce::setting.customer.customer_setting'))
            ->setSectionDescription(trans('plugins/ecommerce::setting.customer.customer_setting_description'))
            ->setValidatorClass(CustomerSettingRequest::class)
            ->add(
                'verify_customer_email',
                OnOffCheckboxField::class,
                OnOffFieldOption::make()
                    ->label(trans('plugins/ecommerce::setting.customer.form.verify_customer_email'))
                    ->helperText(trans('plugins/ecommerce::setting.customer.form.verify_customer_email_helper'))
                    ->value(EcommerceHelper::isEnableEmailVerification())
                    ->toArray()
            )
            ->add(
                'enabled_customer_account_deletion',
                OnOffCheckboxField::class,
                OnOffFieldOption::make()
                    ->label(trans('plugins/ecommerce::setting.customer.form.enabled_customer_account_deletion'))
                    ->helperText(trans('plugins/ecommerce::setting.customer.form.enabled_customer_account_deletion_helper'))
                    ->value(get_ecommerce_setting('enabled_customer_account_deletion', true))
                    ->toArray()
            )
            ->add(
                'enabled_customer_dob_field',
                OnOffCheckboxField::class,
                OnOffFieldOption::make()
                    ->label(trans('plugins/ecommerce::setting.customer.form.enabled_customer_dob_field'))
                    ->helperText(trans('plugins/ecommerce::setting.customer.form.enabled_customer_dob_field_helper'))
                    ->value(get_ecommerce_setting('enabled_customer_dob_field', true))
                    ->toArray()
            )
            ->add(
                'make_customer_phone_number_required',
                OnOffCheckboxField::class,
                OnOffFieldOption::make()
                    ->label(trans('plugins/ecommerce::setting.customer.form.make_customer_phone_number_required'))
                    ->helperText(trans('plugins/ecommerce::setting.customer.form.make_customer_phone_number_required_helper'))
                    ->value(get_ecommerce_setting('make_customer_phone_number_required', false))
                    ->toArray()
            )
            ->add(
                'login_option',
                RadioField::class,
                RadioFieldOption::make()
                    ->label(trans('plugins/ecommerce::setting.customer.form.login_option'))
                    ->selected(EcommerceHelper::getLoginOption())
                    ->choices([
                        'email' => trans('plugins/ecommerce::setting.customer.form.login_with_email'),
                        'phone' => trans('plugins/ecommerce::setting.customer.form.login_with_phone'),
                        'email_or_phone' => trans('plugins/ecommerce::setting.customer.form.login_with_email_or_phone'),
                    ])
                    ->toArray(),
            );
    }
}
