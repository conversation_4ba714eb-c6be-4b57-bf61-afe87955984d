[2025-07-13 12:52:56] local.ERROR: Class "Botble\Ecommerce\Facades\EcommerceHelper" not found {"exception":"[object] (Error(code: 0): Class \"Botble\\Ecommerce\\Facades\\EcommerceHelper\" not found at C:\\Users\\<USER>\\Desktop\\uu\\platform\\plugins\\marketplace\\routes\\fronts.php:111)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(509): Illuminate\\Support\\ServiceProvider->{closure:{closure:{closure:C:\\Users\\<USER>\\Desktop\\uu\\platform\\plugins\\marketplace\\routes\\fronts.php:19}:20}:38}(Object(Illuminate\\Routing\\Router))
#1 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#2 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\uu\\platform\\plugins\\marketplace\\routes\\fronts.php(34): Illuminate\\Support\\Facades\\Facade::__callStatic('group', Array)
#4 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\theme\\src\\Theme.php(874): Illuminate\\Support\\ServiceProvider->{closure:{closure:C:\\Users\\<USER>\\Desktop\\uu\\platform\\plugins\\marketplace\\routes\\fronts.php:19}:20}()
#5 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(509): Botble\\Theme\\Theme->{closure:{closure:Botble\\Theme\\Theme::registerRoutes():873}:874}(Object(Illuminate\\Routing\\Router))
#6 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#7 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#8 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\theme\\src\\Theme.php(874): Illuminate\\Support\\Facades\\Facade::__callStatic('group', Array)
#9 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(509): Botble\\Theme\\Theme->{closure:Botble\\Theme\\Theme::registerRoutes():873}(Object(Illuminate\\Routing\\Router))
#10 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#11 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#12 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\theme\\src\\Theme.php(873): Illuminate\\Support\\Facades\\Facade::__callStatic('group', Array)
#13 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Botble\\Theme\\Theme->registerRoutes(Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\uu\\platform\\plugins\\marketplace\\routes\\fronts.php(20): Illuminate\\Support\\Facades\\Facade::__callStatic('registerRoutes', Array)
#15 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(509): Illuminate\\Support\\ServiceProvider->{closure:C:\\Users\\<USER>\\Desktop\\uu\\platform\\plugins\\marketplace\\routes\\fronts.php:19}(Object(Illuminate\\Routing\\Router))
#16 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#17 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#18 C:\\Users\\<USER>\\Desktop\\uu\\platform\\plugins\\marketplace\\routes\\fronts.php(17): Illuminate\\Support\\Facades\\Facade::__callStatic('group', Array)
#19 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(152): require('C:\\\\Users\\\\<USER>\\\\...')
#20 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\platform\\base\\src\\Traits\\LoadAndPublishDataTrait.php(84): Illuminate\\Support\\ServiceProvider->loadRoutesFrom('C:\\\\Users\\\\<USER>\\\\...')
#21 C:\\Users\\<USER>\\Desktop\\uu\\platform\\plugins\\marketplace\\src\\Providers\\MarketplaceServiceProvider.php(94): Botble\\Marketplace\\Providers\\MarketplaceServiceProvider->loadRoutes(Array)
#22 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Botble\\Marketplace\\Providers\\MarketplaceServiceProvider->boot()
#23 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#24 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Container\\Container->call(Array)
#28 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1015): Illuminate\\Foundation\\Application->bootProvider(Object(Botble\\Marketplace\\Providers\\MarketplaceServiceProvider))
#29 [internal function]: Illuminate\\Foundation\\Application->{closure:Illuminate\\Foundation\\Application::boot():1014}(Object(Botble\\Marketplace\\Providers\\MarketplaceServiceProvider), 86)
#30 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): array_walk(Array, Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#32 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(263): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#33 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#34 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#35 C:\\Users\\<USER>\\Desktop\\uu\\artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 {main}
"} 
[2025-07-13 12:59:51] local.ERROR: Class "Botble\Ecommerce\Facades\EcommerceHelper" not found {"exception":"[object] (Error(code: 0): Class \"Botble\\Ecommerce\\Facades\\EcommerceHelper\" not found at C:\\Users\\<USER>\\Desktop\\uu\\platform\\plugins\\marketplace\\routes\\fronts.php:111)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(509): Illuminate\\Support\\ServiceProvider->{closure:{closure:{closure:C:\\Users\\<USER>\\Desktop\\uu\\platform\\plugins\\marketplace\\routes\\fronts.php:19}:20}:38}(Object(Illuminate\\Routing\\Router))
#1 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#2 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\uu\\platform\\plugins\\marketplace\\routes\\fronts.php(34): Illuminate\\Support\\Facades\\Facade::__callStatic('group', Array)
#4 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\theme\\src\\Theme.php(874): Illuminate\\Support\\ServiceProvider->{closure:{closure:C:\\Users\\<USER>\\Desktop\\uu\\platform\\plugins\\marketplace\\routes\\fronts.php:19}:20}()
#5 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(509): Botble\\Theme\\Theme->{closure:{closure:Botble\\Theme\\Theme::registerRoutes():873}:874}(Object(Illuminate\\Routing\\Router))
#6 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#7 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#8 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\theme\\src\\Theme.php(874): Illuminate\\Support\\Facades\\Facade::__callStatic('group', Array)
#9 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(509): Botble\\Theme\\Theme->{closure:Botble\\Theme\\Theme::registerRoutes():873}(Object(Illuminate\\Routing\\Router))
#10 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#11 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#12 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\theme\\src\\Theme.php(873): Illuminate\\Support\\Facades\\Facade::__callStatic('group', Array)
#13 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Botble\\Theme\\Theme->registerRoutes(Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\uu\\platform\\plugins\\marketplace\\routes\\fronts.php(20): Illuminate\\Support\\Facades\\Facade::__callStatic('registerRoutes', Array)
#15 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(509): Illuminate\\Support\\ServiceProvider->{closure:C:\\Users\\<USER>\\Desktop\\uu\\platform\\plugins\\marketplace\\routes\\fronts.php:19}(Object(Illuminate\\Routing\\Router))
#16 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#17 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#18 C:\\Users\\<USER>\\Desktop\\uu\\platform\\plugins\\marketplace\\routes\\fronts.php(17): Illuminate\\Support\\Facades\\Facade::__callStatic('group', Array)
#19 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(152): require('C:\\\\Users\\\\<USER>\\\\...')
#20 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\platform\\base\\src\\Traits\\LoadAndPublishDataTrait.php(84): Illuminate\\Support\\ServiceProvider->loadRoutesFrom('C:\\\\Users\\\\<USER>\\\\...')
#21 C:\\Users\\<USER>\\Desktop\\uu\\platform\\plugins\\marketplace\\src\\Providers\\MarketplaceServiceProvider.php(94): Botble\\Marketplace\\Providers\\MarketplaceServiceProvider->loadRoutes(Array)
#22 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Botble\\Marketplace\\Providers\\MarketplaceServiceProvider->boot()
#23 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#24 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Container\\Container->call(Array)
#28 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1015): Illuminate\\Foundation\\Application->bootProvider(Object(Botble\\Marketplace\\Providers\\MarketplaceServiceProvider))
#29 [internal function]: Illuminate\\Foundation\\Application->{closure:Illuminate\\Foundation\\Application::boot():1014}(Object(Botble\\Marketplace\\Providers\\MarketplaceServiceProvider), 86)
#30 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): array_walk(Array, Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#32 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(263): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#33 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#34 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#35 C:\\Users\\<USER>\\Desktop\\uu\\artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 {main}
"} 
