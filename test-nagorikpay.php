<?php

// Bootstrap Laravel
require __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== NagorikPay Plugin Test ===\n\n";

// Test 1: Check if plugin is active
$activePlugins = get_active_plugins();
$isActive = in_array('nagorikpay', $activePlugins);
echo "1. Plugin Active: " . ($isActive ? '✅ YES' : '❌ NO') . "\n";

if (!$isActive) {
    echo "   Please activate the plugin first.\n";
    exit;
}

// Test 2: Check constants
$constantsDefined = defined('NAGORIKPAY_PAYMENT_METHOD_NAME');
echo "2. Constants Defined: " . ($constantsDefined ? '✅ YES' : '❌ NO') . "\n";

$methodName = $constantsDefined ? NAGORIKPAY_PAYMENT_METHOD_NAME : 'nagorikpay';

// Test 3: Check settings
echo "3. Settings Check:\n";
$status = get_payment_setting('status', $methodName, 0);
$name = get_payment_setting('name', $methodName, 'Not Set');
$apiKey = get_payment_setting('api_key', $methodName, '');
$mode = get_payment_setting('mode', $methodName, 'Not Set');

echo "   - Status: " . ($status ? '✅ ENABLED' : '❌ DISABLED') . "\n";
echo "   - Name: $name\n";
echo "   - API Key: " . ($apiKey ? '✅ SET' : '❌ NOT SET') . "\n";
echo "   - Mode: $mode\n";

// Test 4: Check service instantiation
echo "4. Service Test: ";
try {
    $service = app(\Botble\NagorikPay\Services\NagorikPayService::class);
    echo "✅ SERVICE CAN BE CREATED\n";

    // Test supported currencies
    $currencies = $service->supportedCurrencyCodes();
    echo "   - Supported currencies: " . implode(', ', $currencies) . "\n";
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
}

// Test 5: Check routes
echo "5. Routes Test:\n";
$routes = [
    'payments.nagorikpay.success',
    'payments.nagorikpay.cancel',
    'payments.nagorikpay.webhook'
];

foreach ($routes as $routeName) {
    try {
        $url = route($routeName);
        echo "   - $routeName: ✅ $url\n";
    } catch (Exception $e) {
        echo "   - $routeName: ❌ ERROR\n";
    }
}

// Test 6: Check payment plugin dependency
$paymentActive = is_plugin_active('payment');
echo "6. Payment Plugin: " . ($paymentActive ? '✅ ACTIVE' : '❌ INACTIVE') . "\n";

// Test 7: Test API connectivity (if API key is set)
if ($apiKey) {
    echo "7. API Test: ";
    try {
        $response = \Illuminate\Support\Facades\Http::timeout(10)->withHeaders([
            'API-KEY' => $apiKey,
            'Content-Type' => 'application/json',
        ])->post('https://secure-pay.nagorikpay.com/api/payment/create', [
            'amount' => '1.00',
            'success_url' => 'https://example.com/success',
            'cancel_url' => 'https://example.com/cancel',
            'webhook_url' => 'https://example.com/webhook',
        ]);
        
        if ($response->successful()) {
            echo "✅ API REACHABLE\n";
        } else {
            echo "⚠️ API RESPONSE: " . $response->status() . "\n";
        }
    } catch (Exception $e) {
        echo "❌ API ERROR: " . $e->getMessage() . "\n";
    }
} else {
    echo "7. API Test: ⏭️ SKIPPED (No API key)\n";
}

echo "\n=== Test Summary ===\n";

if ($isActive && $constantsDefined && $status && $apiKey && $paymentActive) {
    echo "🎉 ALL TESTS PASSED! Plugin should work correctly.\n";
    echo "\nNext steps:\n";
    echo "1. Go to admin panel → Settings → Payment Methods\n";
    echo "2. Verify NagorikPay settings\n";
    echo "3. Test checkout process\n";
} else {
    echo "⚠️ SOME ISSUES FOUND. Please fix the following:\n";
    if (!$isActive) echo "- Activate the NagorikPay plugin\n";
    if (!$constantsDefined) echo "- Constants not loaded properly\n";
    if (!$status) echo "- Enable NagorikPay in payment settings\n";
    if (!$apiKey) echo "- Set the API key in payment settings\n";
    if (!$paymentActive) echo "- Activate the Payment plugin\n";
}

echo "\n=== Test Complete ===\n";
