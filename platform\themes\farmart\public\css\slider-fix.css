/* Fix for slider and gallery initialization issues */

/* Hide uninitialized sliders to prevent vertical layout flash */
.slick-slides-carousel:not(.slick-initialized) {
    visibility: hidden;
    opacity: 0;
    transition: opacity 0.3s ease;
}

/* Show sliders once initialized */
.slick-slides-carousel.slick-initialized {
    visibility: visible;
    opacity: 1;
}

/* Product gallery fixes */
.product-gallery:not(.gallery-initialized),
.bb-product-gallery:not(.gallery-initialized) {
    visibility: hidden;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.product-gallery.gallery-initialized,
.bb-product-gallery.gallery-initialized {
    visibility: visible;
    opacity: 1;
}

/* Ensure images maintain aspect ratio during loading */
.slick-slides-carousel:not(.slick-initialized) img,
.product-gallery:not(.gallery-initialized) img,
.bb-product-gallery:not(.gallery-initialized) img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Prevent layout shift for slider containers */
.section-content__slider .section-slides-wrapper .slick-slides-carousel:not(.slick-initialized) {
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
}

/* Loading placeholder for uninitialized sliders */
.slick-slides-carousel:not(.slick-initialized)::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 1;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Product gallery specific fixes */
.bb-product-gallery-images:not(.slick-initialized) {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.bb-product-gallery-images:not(.slick-initialized) > * {
    margin-bottom: 10px;
}

.bb-product-gallery-images:not(.slick-initialized) > *:last-child {
    margin-bottom: 0;
}

/* Thumbnail gallery fixes */
.bb-product-gallery-thumbnails:not(.slick-initialized) {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
}

.bb-product-gallery-thumbnails:not(.slick-initialized) > * {
    flex: 0 0 auto;
    max-width: 80px;
}

/* Quick view gallery fixes */
.bb-quick-view-gallery-images:not(.slick-initialized) {
    visibility: hidden;
    opacity: 0;
}

.bb-quick-view-gallery-images.slick-initialized {
    visibility: visible;
    opacity: 1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .section-content__slider .section-slides-wrapper .slick-slides-carousel:not(.slick-initialized) {
        min-height: 150px;
    }
    
    .bb-product-gallery-thumbnails:not(.slick-initialized) > * {
        max-width: 60px;
    }
}

/* Ensure smooth transitions */
.slick-slides-carousel,
.product-gallery,
.bb-product-gallery {
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

/* Fix for lazy loaded images in sliders */
.slick-slide img.lazyload {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.slick-slide img.lazyloaded {
    opacity: 1;
}

/* Ensure images have proper dimensions during loading */
.slick-slides-carousel:not(.slick-initialized) img,
.product-gallery:not(.gallery-initialized) img,
.bb-product-gallery:not(.gallery-initialized) img {
    width: 100% !important;
    height: auto !important;
    max-width: 100% !important;
    object-fit: cover;
}

/* Prevent content jumping during initialization */
.slick-slides-carousel:not(.slick-initialized),
.product-gallery:not(.gallery-initialized),
.bb-product-gallery:not(.gallery-initialized) {
    min-height: 200px;
}

/* Specific fixes for product gallery images */
.bb-product-gallery-images:not(.slick-initialized) img {
    max-height: 400px;
    width: auto;
    margin: 0 auto;
    display: block;
}

.bb-product-gallery-thumbnails:not(.slick-initialized) img {
    max-height: 80px;
    width: auto;
}
