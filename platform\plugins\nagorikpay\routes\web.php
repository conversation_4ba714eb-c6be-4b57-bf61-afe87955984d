<?php

use Bo<PERSON>ble\NagorikPay\Http\Controllers\NagorikPayController;
use Illuminate\Support\Facades\Route;

Route::group(['namespace' => 'Botble\NagorikPay\Http\Controllers', 'middleware' => ['web', 'core']], function () {
    Route::group(['prefix' => 'payments/nagorikpay'], function () {
        Route::get('success', [NagorikPayController::class, 'success'])->name('payments.nagorikpay.success');
        Route::get('cancel', [NagorikPayController::class, 'cancel'])->name('payments.nagorikpay.cancel');
        Route::post('webhook', [NagorikPayController::class, 'webhook'])->name('payments.nagorikpay.webhook');
    });
});
