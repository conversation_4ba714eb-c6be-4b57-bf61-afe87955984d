<?php

use Botble\NagorikPay\Http\Controllers\NagorikPayController;
use Illuminate\Support\Facades\Route;

Route::group(['namespace' => 'Botble\NagorikPay\Http\Controllers', 'middleware' => ['web']], function () {
    
    Route::post('payments/nagorikpay/webhook', [NagorikPayController::class, 'webhook'])
        ->name('payments.nagorikpay.webhook');
    
    Route::get('payments/nagorikpay/success', [NagorikPayController::class, 'success'])
        ->name('payments.nagorikpay.success');
    
    Route::get('payments/nagorikpay/cancel', [NagorikPayController::class, 'cancel'])
        ->name('payments.nagorikpay.cancel');
        
});
