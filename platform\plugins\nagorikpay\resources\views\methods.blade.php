@if (get_payment_setting('status', NAGORIKPAY_PAYMENT_METHOD_NAME) == 1)
    <li class="list-group-item">
        <input class="magic-radio js_payment_method" type="radio" name="payment_method" id="payment_{{ NAGORIKPAY_PAYMENT_METHOD_NAME }}"
               value="{{ NAGORIKPAY_PAYMENT_METHOD_NAME }}" data-bs-toggle="collapse"
               data-bs-target=".payment_{{ NAGORIKPAY_PAYMENT_METHOD_NAME }}_wrap"
               data-parent=".list_payment_method"
               @if (setting('default_payment_method') == NAGORIKPAY_PAYMENT_METHOD_NAME) checked @endif>
        <label for="payment_{{ NAGORIKPAY_PAYMENT_METHOD_NAME }}" class="text-start">
            <img class="filter-black" src="{{ url('vendor/core/plugins/nagorikpay/images/nagorikpay.svg') }}" alt="NagorikPay" style="height: 24px;">
            {{ get_payment_setting('name', NAGORIKPAY_PAYMENT_METHOD_NAME) ?: trans('plugins/nagorikpay::nagorikpay.method_name') }}
        </label>
        <div class="payment_{{ NAGORIKPAY_PAYMENT_METHOD_NAME }}_wrap payment_collapse_wrap collapse @if (setting('default_payment_method') == NAGORIKPAY_PAYMENT_METHOD_NAME) show @endif">
            <p>{!! BaseHelper::clean(get_payment_setting('description', NAGORIKPAY_PAYMENT_METHOD_NAME, trans('plugins/nagorikpay::nagorikpay.payment_description'))) !!}</p>
        </div>
    </li>
@endif
