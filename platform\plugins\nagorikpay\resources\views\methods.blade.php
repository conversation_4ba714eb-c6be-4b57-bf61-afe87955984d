@php
    $methodName = defined('NAGORIKPAY_PAYMENT_METHOD_NAME') ? NAGORIKPAY_PAYMENT_METHOD_NAME : 'nagorikpay';
    $supportedCurrencies = ['BDT', 'USD'];
    $currentCurrency = strtoupper(get_application_currency()->title);
@endphp

@if (get_payment_setting('status', $methodName) == 1)
    <li class="list-group-item">
        <input class="magic-radio js_payment_method" type="radio" name="payment_method" id="payment_{{ $methodName }}"
               value="{{ $methodName }}" data-bs-toggle="collapse" data-bs-target=".payment_{{ $methodName }}_wrap"
               data-parent=".list_payment_method"
               @if (!in_array($currentCurrency, $supportedCurrencies)) disabled @endif>
        <label for="payment_{{ $methodName }}" class="text-start">
            <img src="{{ url('vendor/core/plugins/nagorikpay/images/nagorikpay.png') }}" alt="NagorikPay" style="max-height: 20px;">
            {{ get_payment_setting('name', $methodName, 'NagorikPay') }}
        </label>
        <div class="payment_{{ $methodName }}_wrap payment_collapse_wrap collapse @if (!in_array($currentCurrency, $supportedCurrencies)) d-none @endif">
            <div class="card">
                <div class="card-body">
                    @if (!in_array($currentCurrency, $supportedCurrencies))
                        <div class="alert alert-warning">
                            {{ __('NagorikPay does not support :currency. Supported currencies: :currencies', [
                                'currency' => $currentCurrency,
                                'currencies' => implode(', ', $supportedCurrencies)
                            ]) }}
                        </div>
                    @else
                        <p>{!! BaseHelper::clean(get_payment_setting('description', $methodName, 'Pay securely with NagorikPay - supports bKash, Nagad, Rocket and other popular payment methods in Bangladesh')) !!}</p>
                        
                        <div class="payment-info-loading" style="display: none;">
                            <div class="d-flex align-items-center">
                                <div class="spinner-border spinner-border-sm me-2" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                {{ __('Processing payment...') }}
                            </div>
                        </div>
                        
                        <div class="nagorikpay-payment-methods mt-3">
                            <div class="row">
                                <div class="col-md-3 col-6 mb-2">
                                    <div class="payment-method-item text-center p-2 border rounded">
                                        <img src="{{ url('vendor/core/plugins/nagorikpay/images/bkash.png') }}" alt="bKash" class="img-fluid" style="max-height: 30px;">
                                        <small class="d-block mt-1">bKash</small>
                                    </div>
                                </div>
                                <div class="col-md-3 col-6 mb-2">
                                    <div class="payment-method-item text-center p-2 border rounded">
                                        <img src="{{ url('vendor/core/plugins/nagorikpay/images/nagad.png') }}" alt="Nagad" class="img-fluid" style="max-height: 30px;">
                                        <small class="d-block mt-1">Nagad</small>
                                    </div>
                                </div>
                                <div class="col-md-3 col-6 mb-2">
                                    <div class="payment-method-item text-center p-2 border rounded">
                                        <img src="{{ url('vendor/core/plugins/nagorikpay/images/rocket.png') }}" alt="Rocket" class="img-fluid" style="max-height: 30px;">
                                        <small class="d-block mt-1">Rocket</small>
                                    </div>
                                </div>
                                <div class="col-md-3 col-6 mb-2">
                                    <div class="payment-method-item text-center p-2 border rounded">
                                        <img src="{{ url('vendor/core/plugins/nagorikpay/images/card.png') }}" alt="Card" class="img-fluid" style="max-height: 30px;">
                                        <small class="d-block mt-1">Card</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </li>
@endif
