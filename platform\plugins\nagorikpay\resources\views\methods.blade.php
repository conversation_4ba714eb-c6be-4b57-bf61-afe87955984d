@php
    $methodName = defined('NAGORIKPAY_PAYMENT_METHOD_NAME') ? NAGORIKPAY_PAYMENT_METHOD_NAME : 'nagorikpay';
    $supportedCurrencies = ['BDT', 'USD'];
    $currentCurrency = strtoupper(get_application_currency()->title);
@endphp

@if (get_payment_setting('status', $methodName) == 1)
    <li class="list-group-item">
        <input class="magic-radio js_payment_method" type="radio" name="payment_method" id="payment_{{ $methodName }}"
               value="{{ $methodName }}" data-bs-toggle="collapse" data-bs-target=".payment_{{ $methodName }}_wrap"
               data-parent=".list_payment_method"
               @if (!in_array($currentCurrency, $supportedCurrencies)) disabled @endif>
        <label for="payment_{{ $methodName }}" class="text-start">
            {{ get_payment_setting('name', $methodName, 'NagorikPay') }}
        </label>
        <div class="payment_{{ $methodName }}_wrap payment_collapse_wrap collapse @if (!in_array($currentCurrency, $supportedCurrencies)) d-none @endif">
            <div class="card">
                <div class="card-body">
                    @if (!in_array($currentCurrency, $supportedCurrencies))
                        <div class="alert alert-warning">
                            {{ __('NagorikPay does not support :currency. Supported currencies: :currencies', [
                                'currency' => $currentCurrency,
                                'currencies' => implode(', ', $supportedCurrencies)
                            ]) }}
                        </div>
                    @else
                        <p>{!! BaseHelper::clean(get_payment_setting('description', $methodName, 'Pay securely with NagorikPay - supports bKash, Nagad, Rocket and other popular payment methods in Bangladesh')) !!}</p>

                        <div class="nagorikpay-payment-methods mt-3">
                            <h6>Available Payment Methods:</h6>
                            <div class="row">
                                <div class="col-3 mb-2">
                                    <div class="payment-method-item text-center p-2 border rounded">
                                        <strong>bKash</strong>
                                    </div>
                                </div>
                                <div class="col-3 mb-2">
                                    <div class="payment-method-item text-center p-2 border rounded">
                                        <strong>Nagad</strong>
                                    </div>
                                </div>
                                <div class="col-3 mb-2">
                                    <div class="payment-method-item text-center p-2 border rounded">
                                        <strong>Rocket</strong>
                                    </div>
                                </div>
                                <div class="col-3 mb-2">
                                    <div class="payment-method-item text-center p-2 border rounded">
                                        <strong>Cards</strong>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info mt-3">
                            <small>
                                <i class="fa fa-info-circle"></i>
                                You will be redirected to NagorikPay secure payment page after clicking "Place Order".
                            </small>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </li>
@endif
