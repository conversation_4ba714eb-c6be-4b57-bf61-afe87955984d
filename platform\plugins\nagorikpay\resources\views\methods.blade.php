@php
    $nagorikpayMethodName = defined('NAGORIKPAY_PAYMENT_METHOD_NAME') ? NAGORIKPAY_PAYMENT_METHOD_NAME : 'nagorikpay';
@endphp

@if (get_payment_setting('status', $nagorikpayMethodName) == 1)
    <li class="list-group-item">
        <input class="magic-radio js_payment_method" type="radio" name="payment_method" id="payment_{{ $nagorikpayMethodName }}"
               value="{{ $nagorikpayMethodName }}" data-bs-toggle="collapse"
               data-bs-target=".payment_{{ $nagorikpayMethodName }}_wrap"
               data-parent=".list_payment_method"
               @if (setting('default_payment_method') == $nagorikpayMethodName) checked @endif>
        <label for="payment_{{ $nagorikpayMethodName }}" class="text-start">
            <img class="filter-black" src="{{ url('vendor/core/plugins/nagorikpay/images/nagorikpay.svg') }}" alt="NagorikPay" style="height: 24px;">
            {{ get_payment_setting('name', $nagorikpayMethodName) ?: trans('plugins/nagorikpay::nagorikpay.method_name') }}
        </label>
        <div class="payment_{{ $nagorikpayMethodName }}_wrap payment_collapse_wrap collapse @if (setting('default_payment_method') == $nagorikpayMethodName) show @endif">
            <p>{!! BaseHelper::clean(get_payment_setting('description', $nagorikpayMethodName, trans('plugins/nagorikpay::nagorikpay.payment_description'))) !!}</p>
        </div>
    </li>
@endif
