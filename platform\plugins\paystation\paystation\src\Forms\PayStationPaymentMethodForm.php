<?php

namespace Bo<PERSON>ble\PayStation\Forms;

use Botble\Base\Facades\BaseHelper;
use Botble\Base\Forms\FieldOptions\SelectFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\TextFieldOption;
use Botble\Base\Forms\Fields\SelectField;
use Botble\Base\Forms\Fields\TextField;
use Botble\Payment\Forms\PaymentMethodForm;

class PayStationPaymentMethodForm extends PaymentMethodForm
{
    public function setup(): void
    {
        parent::setup();

        $this
            ->paymentId(PAYSTATION_PAYMENT_METHOD_NAME)
            ->paymentName('PayStation')
            ->paymentDescription('PayStation payment gateway - supports bKash, Nagad, Rocket, Cards and other payment methods in Bangladesh')
            ->paymentLogo(url('vendor/core/plugins/paystation/images/paystation.png'))
            ->paymentUrl('https://paystation.com.bd')
            ->paymentInstructions(view('plugins/paystation::instructions')->render())
            ->add(
                'payment_paystation_merchant_id',
                TextField::class,
                TextFieldOption::make()
                    ->label('Merchant ID')
                    ->defaultValue(BaseHelper::hasDemoModeEnabled() ? '*******************************' : get_payment_setting('merchant_id', PAYSTATION_PAYMENT_METHOD_NAME))
                    ->placeholder('204-16537301811')
                    ->attributes(['data-counter' => 100])
                    ->toArray()
            )
            ->add(
                'payment_paystation_password',
                'password',
                TextFieldOption::make()
                    ->label('Password')
                    ->defaultValue(BaseHelper::hasDemoModeEnabled() ? '*******************************' : get_payment_setting('password', PAYSTATION_PAYMENT_METHOD_NAME))
                    ->placeholder('Enter your PayStation password')
                    ->toArray()
            )
            ->add(
                'payment_' . PAYSTATION_PAYMENT_METHOD_NAME . '_environment',
                SelectField::class,
                SelectFieldOption::make()
                    ->label('Environment')
                    ->choices([
                        'sandbox' => 'Sandbox (Test)',
                        'live' => 'Live (Production)',
                    ])
                    ->selected(get_payment_setting('environment', PAYSTATION_PAYMENT_METHOD_NAME, 'sandbox'))
                    ->toArray()
            )
            ->add(
                'payment_paystation_currency_rate',
                TextField::class,
                TextFieldOption::make()
                    ->label('USD to BDT Rate')
                    ->defaultValue(get_payment_setting('currency_rate', PAYSTATION_PAYMENT_METHOD_NAME, '110'))
                    ->placeholder('110')
                    ->attributes(['type' => 'number', 'step' => '0.01'])
                    ->toArray()
            );
    }
}
