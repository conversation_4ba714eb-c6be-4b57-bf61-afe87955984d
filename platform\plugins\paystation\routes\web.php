<?php

use Botble\PayStation\Http\Controllers\PayStationController;
use Illuminate\Support\Facades\Route;

Route::group(['namespace' => 'Botble\PayStation\Http\Controllers', 'middleware' => ['web']], function () {
    Route::group(['prefix' => 'payments/paystation'], function () {
        Route::get('callback', [PayStationController::class, 'callback'])->name('payments.paystation.callback');
        Route::post('callback', [PayStationController::class, 'callback'])->name('payments.paystation.callback.post');
        Route::get('success', [PayStationController::class, 'success'])->name('payments.paystation.success');
        Route::get('cancel', [PayStationController::class, 'cancel'])->name('payments.paystation.cancel');
    });
});
