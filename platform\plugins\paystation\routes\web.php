<?php

use Botble\PayStation\Http\Controllers\PayStationController;
use Illuminate\Support\Facades\Route;

Route::group(['namespace' => 'Botble\PayStation\Http\Controllers', 'middleware' => ['web']], function () {
    Route::group(['prefix' => 'payments/paystation'], function () {
        Route::get('callback', [PayStationController::class, 'callback'])->name('payments.paystation.callback');
        Route::post('callback', [PayStationController::class, 'callback'])->name('payments.paystation.callback.post');
        Route::post('webhook', [PayStationController::class, 'webhook'])->name('payments.paystation.webhook');
        Route::get('success', [PayStationController::class, 'success'])->name('payments.paystation.success');
        Route::get('failed', [PayStationController::class, 'failed'])->name('payments.paystation.failed');
        Route::get('cancel', [PayStationController::class, 'cancel'])->name('payments.paystation.cancel');
        Route::get('test-callback', [PayStationController::class, 'testCallback'])->name('payments.paystation.test');
    });
});
