<?php

// Bootstrap Laravel
require __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Botble\Setting\Facades\Setting;

echo "=== NagorikPay Settings Fix ===\n\n";

// Force save the settings directly
$settings = [
    'payment_nagorikpay_status' => '1',
    'payment_nagorikpay_name' => 'NagorikPay',
    'payment_nagorikpay_description' => 'Pay securely with NagorikPay - supports bKash, Nagad, Rocket and other popular payment methods in Bangladesh',
    'payment_nagorikpay_api_key' => 'gnXi7etgWNhFyFGZFrOMYyrmnF4A1eGU5SC2QRmUvILOlNc2Ef',
    'payment_nagorikpay_mode' => 'sandbox',
];

echo "Forcing NagorikPay settings...\n";

foreach ($settings as $key => $value) {
    Setting::set($key, $value);
    echo "✅ $key = $value\n";
}

Setting::save();

echo "\n🎉 Settings saved successfully!\n";

// Verify the settings
echo "\nVerifying settings:\n";
foreach ($settings as $key => $expectedValue) {
    $actualValue = Setting::get($key);
    $status = ($actualValue == $expectedValue) ? '✅' : '❌';
    echo "$status $key: $actualValue\n";
}

// Clear cache
echo "\nClearing cache...\n";
try {
    \Illuminate\Support\Facades\Artisan::call('cache:clear');
    echo "✅ Cache cleared\n";
} catch (Exception $e) {
    echo "⚠️ Cache clear failed: " . $e->getMessage() . "\n";
}

try {
    \Illuminate\Support\Facades\Artisan::call('config:clear');
    echo "✅ Config cache cleared\n";
} catch (Exception $e) {
    echo "⚠️ Config clear failed: " . $e->getMessage() . "\n";
}

// Test if the plugin is working
echo "\nTesting plugin functionality...\n";
try {
    $service = app(\Botble\NagorikPay\Services\NagorikPayService::class);
    echo "✅ Service can be instantiated\n";

    $currencies = $service->supportedCurrencyCodes();
    echo "✅ Supported currencies: " . implode(', ', $currencies) . "\n";
} catch (Exception $e) {
    echo "❌ Service error: " . $e->getMessage() . "\n";
}

echo "\n=== Fix Complete ===\n";
echo "Now try:\n";
echo "1. Clear cache: php artisan cache:clear\n";
echo "2. Refresh your admin panel\n";
echo "3. Go to Settings → Payment Methods\n";
echo "4. Check if NagorikPay is now visible\n";
echo "5. Test checkout process\n";
