<?php

namespace Bo<PERSON>ble\Ecommerce\Facades;

use Bo<PERSON>ble\Ecommerce\Supports\OrderReturnHelper as BaseOrderReturnHelper;
use Illuminate\Support\Facades\Facade;

/**
 * @method static array returnOrder(\Botble\Ecommerce\Models\Order $order, array $data)
 * @method static array cancelReturnOrder(\Botble\Ecommerce\Models\OrderReturn $orderReturn, string|null $reason = null)
 * @method static array updateReturnOrder(\Botble\Ecommerce\Models\OrderReturn $orderReturn, array $data)
 *
 * @see \Botble\Ecommerce\Supports\OrderReturnHelper
 */
class OrderReturnHelper extends Facade
{
    protected static function getFacadeAccessor(): string
    {
        return BaseOrderReturnHelper::class;
    }
}
