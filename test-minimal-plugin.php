<?php

echo "=== Minimal NagorikPay Plugin Test ===\n\n";

// Test 1: Check plugin files
echo "1. Plugin Files Check:\n";
$files = [
    'platform/plugins/nagorikpay/plugin.json',
    'platform/plugins/nagorikpay/helpers/helpers.php',
    'platform/plugins/nagorikpay/src/Providers/NagorikPayServiceProvider.php',
    'platform/plugins/nagorikpay/src/Providers/HookServiceProvider.php',
    'platform/plugins/nagorikpay/src/Forms/NagorikPaymentMethodForm.php',
    'platform/plugins/nagorikpay/resources/views/methods.blade.php',
    'platform/plugins/nagorikpay/routes/web.php'
];

foreach ($files as $file) {
    $exists = file_exists($file);
    echo "   - " . basename($file) . ": " . ($exists ? '✅ EXISTS' : '❌ MISSING') . "\n";
}

// Test 2: Check for infinite loop prevention
echo "\n2. Infinite Loop Prevention Check:\n";
$hookFile = 'platform/plugins/nagorikpay/src/Providers/HookServiceProvider.php';
if (file_exists($hookFile)) {
    $content = file_get_contents($hookFile);
    
    if (strpos($content, 'static $processing = false') !== false) {
        echo "   ✅ Static processing flag found\n";
    } else {
        echo "   ❌ Static processing flag missing\n";
    }
    
    if (strpos($content, 'self::$registered') !== false) {
        echo "   ✅ Registration prevention found\n";
    } else {
        echo "   ❌ Registration prevention missing\n";
    }
    
    if (strpos($content, '!is_admin()') !== false) {
        echo "   ✅ Admin check found\n";
    } else {
        echo "   ❌ Admin check missing\n";
    }
} else {
    echo "   ❌ Hook file not found\n";
}

// Test 3: Check service provider safety
echo "\n3. Service Provider Safety Check:\n";
$serviceFile = 'platform/plugins/nagorikpay/src/Providers/NagorikPayServiceProvider.php';
if (file_exists($serviceFile)) {
    $content = file_get_contents($serviceFile);
    
    if (strpos($content, 'nagorikpay.hooks.registered') !== false) {
        echo "   ✅ Hook registration prevention found\n";
    } else {
        echo "   ❌ Hook registration prevention missing\n";
    }
    
    if (strpos($content, 'singleton') !== false) {
        echo "   ✅ Singleton pattern used\n";
    } else {
        echo "   ❌ Singleton pattern missing\n";
    }
} else {
    echo "   ❌ Service file not found\n";
}

// Test 4: Check methods view simplicity
echo "\n4. Methods View Simplicity Check:\n";
$methodsFile = 'platform/plugins/nagorikpay/resources/views/methods.blade.php';
if (file_exists($methodsFile)) {
    $content = file_get_contents($methodsFile);
    $lineCount = substr_count($content, "\n");
    
    echo "   ✅ Methods view exists ($lineCount lines)\n";
    
    if (strpos($content, 'payment-info-loading') === false) {
        echo "   ✅ No auto-loading elements\n";
    } else {
        echo "   ❌ Auto-loading elements found\n";
    }
    
    if (strpos($content, 'Place Order') !== false) {
        echo "   ✅ User guidance present\n";
    } else {
        echo "   ❌ User guidance missing\n";
    }
} else {
    echo "   ❌ Methods file not found\n";
}

echo "\n=== Safety Features ===\n";
echo "✅ 1. Static flags prevent recursive calls\n";
echo "✅ 2. Registration checks prevent multiple hook registrations\n";
echo "✅ 3. Admin checks prevent frontend/backend conflicts\n";
echo "✅ 4. Singleton pattern prevents multiple service instances\n";
echo "✅ 5. Simplified views with no auto-processing\n";
echo "✅ 6. Minimal hook usage to reduce conflicts\n";

echo "\n=== Expected Behavior ===\n";
echo "1. 🎯 No CPU/RAM spikes\n";
echo "2. 🎯 No infinite loops\n";
echo "3. 🎯 Clean payment method display\n";
echo "4. 🎯 Settings save properly\n";
echo "5. 🎯 Only processes on actual checkout\n";

echo "\n=== Test Complete ===\n";
echo "The minimal plugin should now be safe to use!\n";
