<?php

echo "=== PayStation Plugin Activation ===\n\n";

// Read existing plugins.json
$pluginsFile = 'storage/app/plugins.json';
$plugins = [];

if (file_exists($pluginsFile)) {
    $plugins = json_decode(file_get_contents($pluginsFile), true) ?: [];
    echo "Reading existing plugins.json...\n";
} else {
    echo "Creating new plugins.json...\n";
}

// Ensure payment plugin is active first
if (!in_array('payment', $plugins)) {
    $plugins[] = 'payment';
    echo "✅ Payment plugin added\n";
} else {
    echo "✅ Payment plugin already active\n";
}

// Add PayStation plugin
if (!in_array('paystation', $plugins)) {
    $plugins[] = 'paystation';
    echo "✅ PayStation plugin added\n";
} else {
    echo "✅ PayStation plugin already active\n";
}

// Save plugins.json
file_put_contents($pluginsFile, json_encode($plugins, JSON_PRETTY_PRINT));
echo "✅ Saved plugins.json\n\n";

echo "Active plugins:\n";
foreach ($plugins as $plugin) {
    echo "  - $plugin\n";
}

echo "\n=== Activation Complete ===\n";
echo "Now try accessing the admin panel:\n";
echo "1. Go to Settings → Payment Methods\n";
echo "2. PayStation should now appear in the list\n";
echo "3. Configure your PayStation credentials\n";
echo "4. Set environment to Sandbox for testing\n\n";

echo "=== Environment Setup ===\n";
echo "Add these to your .env file:\n";
echo "PAYSTATION_MERCHANT_ID=your-merchant-id\n";
echo "PAYSTATION_PASSWORD=your-password\n";
echo "PAYSTATION_ENVIRONMENT=sandbox\n\n";

echo "PayStation plugin is now ready to use! 🚀\n";
