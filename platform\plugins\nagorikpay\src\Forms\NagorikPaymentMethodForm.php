<?php

namespace Botble\NagorikPay\Forms;

use Bo<PERSON>ble\Base\Forms\FormAbstract;
use Bo<PERSON>ble\Payment\Enums\PaymentMethodEnum;

class NagorikPaymentMethodForm extends FormAbstract
{
    public function buildForm(): void
    {
        $this
            ->setupModel(new \stdClass())
            ->setValidatorClass(\Botble\Payment\Http\Requests\PaymentMethodRequest::class)
            ->withCustomFields()
            ->add('payment_nagorikpay_name', 'text', [
                'label' => trans('plugins/nagorikpay::nagorikpay.settings.name'),
                'label_attr' => ['class' => 'control-label required'],
                'value' => get_payment_setting('name', NAGORIKPAY_PAYMENT_METHOD_NAME, trans('plugins/nagorikpay::nagorikpay.method_name')),
                'attr' => [
                    'required' => true,
                    'data-counter' => 400,
                ],
            ])
            ->add('payment_nagorikpay_description', 'textarea', [
                'label' => trans('plugins/nagorikpay::nagorikpay.settings.description'),
                'label_attr' => ['class' => 'control-label'],
                'value' => get_payment_setting('description', NAGORIKPAY_PAYMENT_METHOD_NAME, trans('plugins/nagorikpay::nagorikpay.payment_description')),
                'attr' => [
                    'rows' => 3,
                    'placeholder' => trans('plugins/nagorikpay::nagorikpay.payment_description'),
                ],
            ])
            ->add('payment_nagorikpay_api_key', 'text', [
                'label' => trans('plugins/nagorikpay::nagorikpay.settings.api_key'),
                'label_attr' => ['class' => 'control-label required'],
                'value' => get_payment_setting('api_key', NAGORIKPAY_PAYMENT_METHOD_NAME),
                'attr' => [
                    'required' => true,
                    'placeholder' => trans('plugins/nagorikpay::nagorikpay.settings.api_key_placeholder'),
                ],
            ])
            ->add('payment_nagorikpay_mode', 'customSelect', [
                'label' => trans('plugins/nagorikpay::nagorikpay.settings.mode'),
                'label_attr' => ['class' => 'control-label required'],
                'choices' => [
                    'sandbox' => trans('plugins/nagorikpay::nagorikpay.settings.sandbox'),
                    'live' => trans('plugins/nagorikpay::nagorikpay.settings.live'),
                ],
                'value' => get_payment_setting('mode', NAGORIKPAY_PAYMENT_METHOD_NAME, 'sandbox'),
                'attr' => [
                    'required' => true,
                ],
            ])
            ->add('payment_nagorikpay_status', 'onOff', [
                'label' => trans('plugins/nagorikpay::nagorikpay.settings.activate'),
                'label_attr' => ['class' => 'control-label'],
                'default_value' => get_payment_setting('status', NAGORIKPAY_PAYMENT_METHOD_NAME, 0),
            ]);
    }
}
