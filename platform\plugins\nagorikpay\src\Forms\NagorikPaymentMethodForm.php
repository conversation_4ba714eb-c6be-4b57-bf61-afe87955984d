<?php

namespace Bo<PERSON>ble\NagorikPay\Forms;

use Botble\Base\Forms\FieldOptions\TextFieldOption;
use Botble\Base\Forms\Fields\TextField;
use Bo<PERSON>ble\Payment\Forms\PaymentMethodForm;

class NagorikPaymentMethodForm extends PaymentMethodForm
{
    public function setup(): void
    {
        parent::setup();

        $this
            ->paymentId(NAGORIKPAY_PAYMENT_METHOD_NAME)
            ->paymentName('NagorikPay')
            ->paymentDescription('Pay with bKash, Nagad, Rocket and cards via NagorikPay')
            ->paymentUrl('https://nagorikpay.com')
            ->add(
                'payment_nagorikpay_api_key',
                TextField::class,
                TextFieldOption::make()
                    ->label('API Key')
                    ->placeholder('Enter your NagorikPay API Key')
                    ->value(get_payment_setting('api_key', NAGORIKPAY_PAYMENT_METHOD_NAME, 'gnXi7etgWNhFyFGZFrOMYyrmnF4A1eGU5SC2QRmUvILOlNc2Ef'))
                    ->toArray()
            );
    }
}
