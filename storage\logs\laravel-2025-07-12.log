[2025-07-12 10:33:54] production.ERROR: The license url field is required. {"userId":1,"exception":"[object] (Exception(code: 0): The license url field is required. at /home/<USER>/public_html/vendor/botble/plugin-management/src/Services/MarketplaceService.php:66)
[stacktrace]
#0 /home/<USER>/public_html/vendor/botble/plugin-management/src/Services/MarketplaceService.php(104): Bo<PERSON><PERSON>\\PluginManagement\\Services\\MarketplaceService->callApi()
#1 /home/<USER>/public_html/vendor/botble/plugin-management/src/Http/Controllers/MarketplaceController.php(135): Botble\\PluginManagement\\Services\\MarketplaceService->beginInstall()
#2 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): Bo<PERSON><PERSON>\\PluginManagement\\Http\\Controllers\\MarketplaceController->update()
#3 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#4 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#5 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#6 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#7 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#8 /home/<USER>/public_html/vendor/botble/platform/base/src/Http/Middleware/DisableInDemoModeMiddleware.php(18): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#9 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Base\\Http\\Middleware\\DisableInDemoModeMiddleware->handle()
#10 /home/<USER>/public_html/vendor/botble/platform/base/src/Http/Middleware/CoreMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#11 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Botble\\Base\\Http\\Middleware\\CoreMiddleware->Botble\\Base\\Http\\Middleware\\{closure}()
#12 /home/<USER>/public_html/vendor/botble/platform/base/src/Http/Middleware/EnsureLicenseHasBeenActivated.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Base\\Http\\Middleware\\EnsureLicenseHasBeenActivated->handle()
#14 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#15 /home/<USER>/public_html/vendor/botble/platform/base/src/Http/Middleware/CoreMiddleware.php(18): Illuminate\\Pipeline\\Pipeline->then()
#16 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Base\\Http\\Middleware\\CoreMiddleware->handle()
#17 /home/<USER>/public_html/platform/plugins/ecommerce/src/Http/Middleware/CaptureCouponMiddleware.php(14): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Ecommerce\\Http\\Middleware\\CaptureCouponMiddleware->handle()
#19 /home/<USER>/public_html/platform/plugins/ecommerce/src/Http/Middleware/CaptureFootprintsMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Ecommerce\\Http\\Middleware\\CaptureFootprintsMiddleware->handle()
#21 /home/<USER>/public_html/vendor/botble/platform/base/src/Http/Middleware/HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle()
#23 /home/<USER>/public_html/vendor/botble/platform/base/src/Http/Middleware/AdminLocaleMiddleware.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Base\\Http\\Middleware\\AdminLocaleMiddleware->handle()
#25 /home/<USER>/public_html/vendor/botble/platform/base/src/Http/Middleware/LocaleMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Base\\Http\\Middleware\\LocaleMiddleware->handle()
#27 /home/<USER>/public_html/vendor/botble/installer/src/Http/Middleware/RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle()
#29 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#31 /home/<USER>/public_html/vendor/botble/platform/acl/src/Http/Middleware/Authenticate.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\ACL\\Http\\Middleware\\Authenticate->handle()
#33 /home/<USER>/public_html/vendor/botble/platform/support/src/Http/Middleware/BaseMiddleware.php(12): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Support\\Http\\Middleware\\BaseMiddleware->handle()
#35 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#37 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#39 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#40 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#42 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#44 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#46 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#47 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#48 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#49 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#50 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#51 /home/<USER>/public_html/vendor/botble/platform/js-validation/src/RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\JsValidation\\RemoteValidationMiddleware->handle()
#53 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#55 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#56 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#58 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#59 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#60 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#61 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#62 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#63 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#64 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#65 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#66 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#67 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#68 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#69 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#70 /home/<USER>/public_html/public/index.php(24): Illuminate\\Foundation\\Http\\Kernel->handle()
#71 {main}
"} 
[2025-07-12 12:33:37] production.ERROR: Call to undefined function Botble\PayStation\Providers\is_admin() {"userId":1,"exception":"[object] (Error(code: 0): Call to undefined function Botble\\PayStation\\Providers\\is_admin() at /home/<USER>/public_html/platform/plugins/paystation/src/Providers/HookServiceProvider.php:28)
[stacktrace]
#0 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Botble\\PayStation\\Providers\\HookServiceProvider->boot()
#1 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#3 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()
#4 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#5 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1034): Illuminate\\Container\\Container->call()
#6 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(817): Illuminate\\Foundation\\Application->bootProvider()
#7 /home/<USER>/public_html/platform/plugins/paystation/src/Providers/PayStationServiceProvider.php(30): Illuminate\\Foundation\\Application->register()
#8 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Botble\\PayStation\\Providers\\PayStationServiceProvider->boot()
#9 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#11 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()
#12 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#13 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1034): Illuminate\\Container\\Container->call()
#14 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(817): Illuminate\\Foundation\\Application->bootProvider()
#15 /home/<USER>/public_html/vendor/botble/plugin-management/src/Services/PluginService.php(97): Illuminate\\Foundation\\Application->register()
#16 /home/<USER>/public_html/vendor/botble/plugin-management/src/Http/Controllers/PluginManagementController.php(108): Botble\\PluginManagement\\Services\\PluginService->activate()
#17 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): Botble\\PluginManagement\\Http\\Controllers\\PluginManagementController->update()
#18 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#19 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#20 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#21 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#22 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#23 /home/<USER>/public_html/vendor/botble/platform/base/src/Http/Middleware/DisableInDemoModeMiddleware.php(18): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Base\\Http\\Middleware\\DisableInDemoModeMiddleware->handle()
#25 /home/<USER>/public_html/vendor/botble/platform/base/src/Http/Middleware/CoreMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Botble\\Base\\Http\\Middleware\\CoreMiddleware->Botble\\Base\\Http\\Middleware\\{closure}()
#27 /home/<USER>/public_html/vendor/botble/platform/base/src/Http/Middleware/EnsureLicenseHasBeenActivated.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Base\\Http\\Middleware\\EnsureLicenseHasBeenActivated->handle()
#29 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 /home/<USER>/public_html/vendor/botble/platform/base/src/Http/Middleware/CoreMiddleware.php(18): Illuminate\\Pipeline\\Pipeline->then()
#31 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Base\\Http\\Middleware\\CoreMiddleware->handle()
#32 /home/<USER>/public_html/platform/plugins/ecommerce/src/Http/Middleware/CaptureCouponMiddleware.php(14): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Ecommerce\\Http\\Middleware\\CaptureCouponMiddleware->handle()
#34 /home/<USER>/public_html/platform/plugins/ecommerce/src/Http/Middleware/CaptureFootprintsMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Ecommerce\\Http\\Middleware\\CaptureFootprintsMiddleware->handle()
#36 /home/<USER>/public_html/vendor/botble/platform/base/src/Http/Middleware/HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle()
#38 /home/<USER>/public_html/vendor/botble/platform/base/src/Http/Middleware/AdminLocaleMiddleware.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Base\\Http\\Middleware\\AdminLocaleMiddleware->handle()
#40 /home/<USER>/public_html/vendor/botble/platform/base/src/Http/Middleware/LocaleMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Base\\Http\\Middleware\\LocaleMiddleware->handle()
#42 /home/<USER>/public_html/vendor/botble/installer/src/Http/Middleware/RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle()
#44 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#46 /home/<USER>/public_html/vendor/botble/platform/acl/src/Http/Middleware/Authenticate.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\ACL\\Http\\Middleware\\Authenticate->handle()
#48 /home/<USER>/public_html/vendor/botble/platform/support/src/Http/Middleware/BaseMiddleware.php(12): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Support\\Http\\Middleware\\BaseMiddleware->handle()
#50 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#52 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#54 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#55 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#57 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#58 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#59 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#60 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#61 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#62 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#63 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#64 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#65 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#66 /home/<USER>/public_html/vendor/botble/platform/js-validation/src/RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#67 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\JsValidation\\RemoteValidationMiddleware->handle()
#68 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#69 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#70 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#71 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#72 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#73 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#74 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#75 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#76 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#77 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#78 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#79 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#80 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#81 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#82 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#83 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#84 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#85 /home/<USER>/public_html/public/index.php(24): Illuminate\\Foundation\\Http\\Kernel->handle()
#86 {main}
"} 
[2025-07-12 12:44:47] production.ERROR: Call to undefined method Botble\Base\Forms\FieldOptions\SelectFieldOption::value() (View: /home/<USER>/public_html/platform/plugins/payment/resources/views/settings/index.blade.php) {"userId":1,"exception":"[object] (Illuminate\\View\\ViewException(code: 0): Call to undefined method Botble\\Base\\Forms\\FieldOptions\\SelectFieldOption::value() (View: /home/<USER>/public_html/platform/plugins/payment/resources/views/settings/index.blade.php) at /home/<USER>/public_html/platform/plugins/paystation/src/Forms/PayStationPaymentMethodForm.php:53)
[stacktrace]
#0 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(60): Illuminate\\View\\Engines\\CompilerEngine->handleViewException()
#1 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#2 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/View/View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get()
#3 /home/<USER>/public_html/vendor/botble/shortcode/src/View/View.php(50): Illuminate\\View\\View->getContents()
#4 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/View/View.php(159): Botble\\Shortcode\\View\\View->renderContents()
#5 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Http/Response.php(69): Illuminate\\View\\View->render()
#6 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Http/Response.php(35): Illuminate\\Http\\Response->setContent()
#7 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(918): Illuminate\\Http\\Response->__construct()
#8 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(885): Illuminate\\Routing\\Router::toResponse()
#9 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Router->prepareResponse()
#10 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#11 /home/<USER>/public_html/vendor/botble/platform/base/src/Http/Middleware/CoreMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#12 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Botble\\Base\\Http\\Middleware\\CoreMiddleware->Botble\\Base\\Http\\Middleware\\{closure}()
#13 /home/<USER>/public_html/vendor/botble/platform/base/src/Http/Middleware/EnsureLicenseHasBeenActivated.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#14 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Base\\Http\\Middleware\\EnsureLicenseHasBeenActivated->handle()
#15 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 /home/<USER>/public_html/vendor/botble/platform/base/src/Http/Middleware/CoreMiddleware.php(18): Illuminate\\Pipeline\\Pipeline->then()
#17 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Base\\Http\\Middleware\\CoreMiddleware->handle()
#18 /home/<USER>/public_html/platform/plugins/ecommerce/src/Http/Middleware/CaptureCouponMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Ecommerce\\Http\\Middleware\\CaptureCouponMiddleware->handle()
#20 /home/<USER>/public_html/platform/plugins/ecommerce/src/Http/Middleware/CaptureFootprintsMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Ecommerce\\Http\\Middleware\\CaptureFootprintsMiddleware->handle()
#22 /home/<USER>/public_html/vendor/botble/platform/base/src/Http/Middleware/HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle()
#24 /home/<USER>/public_html/vendor/botble/platform/base/src/Http/Middleware/AdminLocaleMiddleware.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Base\\Http\\Middleware\\AdminLocaleMiddleware->handle()
#26 /home/<USER>/public_html/vendor/botble/platform/base/src/Http/Middleware/LocaleMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Base\\Http\\Middleware\\LocaleMiddleware->handle()
#28 /home/<USER>/public_html/vendor/botble/installer/src/Http/Middleware/RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle()
#30 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#32 /home/<USER>/public_html/vendor/botble/platform/acl/src/Http/Middleware/Authenticate.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\ACL\\Http\\Middleware\\Authenticate->handle()
#34 /home/<USER>/public_html/vendor/botble/platform/support/src/Http/Middleware/BaseMiddleware.php(12): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Support\\Http\\Middleware\\BaseMiddleware->handle()
#36 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#38 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#40 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#41 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#43 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#45 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#47 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#48 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#49 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#50 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#51 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#52 /home/<USER>/public_html/vendor/botble/platform/js-validation/src/RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\JsValidation\\RemoteValidationMiddleware->handle()
#54 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#56 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#57 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#58 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#59 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#60 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#61 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#62 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#63 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#64 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#65 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#66 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#67 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#68 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#69 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#70 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#71 /home/<USER>/public_html/public/index.php(24): Illuminate\\Foundation\\Http\\Kernel->handle()
#72 {main}

[previous exception] [object] (Error(code: 0): Call to undefined method Botble\\Base\\Forms\\FieldOptions\\SelectFieldOption::value() at /home/<USER>/public_html/platform/plugins/paystation/src/Forms/PayStationPaymentMethodForm.php:53)
[stacktrace]
#0 /home/<USER>/public_html/vendor/botble/platform/base/src/Forms/FormAbstract.php(98): Botble\\PayStation\\Forms\\PayStationPaymentMethodForm->setup()
#1 /home/<USER>/public_html/vendor/botble/form-builder/src/FormBuilder.php(44): Botble\\Base\\Forms\\FormAbstract->buildForm()
#2 /home/<USER>/public_html/vendor/botble/platform/base/src/Forms/FormBuilder.php(11): Kris\\LaravelFormBuilder\\FormBuilder->create()
#3 /home/<USER>/public_html/vendor/botble/platform/base/src/Forms/FormAbstract.php(566): Botble\\Base\\Forms\\FormBuilder->create()
#4 /home/<USER>/public_html/platform/plugins/paystation/src/Providers/HookServiceProvider.php(51): Botble\\Base\\Forms\\FormAbstract::create()
#5 [internal function]: Botble\\PayStation\\Providers\\HookServiceProvider->addPaymentSettings()
#6 /home/<USER>/public_html/vendor/botble/platform/base/src/Supports/Filter.php(25): call_user_func_array()
#7 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(355): Botble\\Base\\Supports\\Filter->fire()
#8 /home/<USER>/public_html/platform/core/base/helpers/action-filter.php(39): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 /home/<USER>/public_html/storage/framework/views/7d2e08a6a4d48c88e5481e26caf60473.php(19): apply_filters()
#10 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(123): require('/home/<USER>/...')
#11 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#12 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire()
#13 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#14 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/View/View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get()
#15 /home/<USER>/public_html/vendor/botble/shortcode/src/View/View.php(50): Illuminate\\View\\View->getContents()
#16 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/View/View.php(159): Botble\\Shortcode\\View\\View->renderContents()
#17 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Http/Response.php(69): Illuminate\\View\\View->render()
#18 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Http/Response.php(35): Illuminate\\Http\\Response->setContent()
#19 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(918): Illuminate\\Http\\Response->__construct()
#20 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(885): Illuminate\\Routing\\Router::toResponse()
#21 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Router->prepareResponse()
#22 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#23 /home/<USER>/public_html/vendor/botble/platform/base/src/Http/Middleware/CoreMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Botble\\Base\\Http\\Middleware\\CoreMiddleware->Botble\\Base\\Http\\Middleware\\{closure}()
#25 /home/<USER>/public_html/vendor/botble/platform/base/src/Http/Middleware/EnsureLicenseHasBeenActivated.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Base\\Http\\Middleware\\EnsureLicenseHasBeenActivated->handle()
#27 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /home/<USER>/public_html/vendor/botble/platform/base/src/Http/Middleware/CoreMiddleware.php(18): Illuminate\\Pipeline\\Pipeline->then()
#29 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Base\\Http\\Middleware\\CoreMiddleware->handle()
#30 /home/<USER>/public_html/platform/plugins/ecommerce/src/Http/Middleware/CaptureCouponMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Ecommerce\\Http\\Middleware\\CaptureCouponMiddleware->handle()
#32 /home/<USER>/public_html/platform/plugins/ecommerce/src/Http/Middleware/CaptureFootprintsMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Ecommerce\\Http\\Middleware\\CaptureFootprintsMiddleware->handle()
#34 /home/<USER>/public_html/vendor/botble/platform/base/src/Http/Middleware/HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle()
#36 /home/<USER>/public_html/vendor/botble/platform/base/src/Http/Middleware/AdminLocaleMiddleware.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Base\\Http\\Middleware\\AdminLocaleMiddleware->handle()
#38 /home/<USER>/public_html/vendor/botble/platform/base/src/Http/Middleware/LocaleMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Base\\Http\\Middleware\\LocaleMiddleware->handle()
#40 /home/<USER>/public_html/vendor/botble/installer/src/Http/Middleware/RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle()
#42 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#44 /home/<USER>/public_html/vendor/botble/platform/acl/src/Http/Middleware/Authenticate.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\ACL\\Http\\Middleware\\Authenticate->handle()
#46 /home/<USER>/public_html/vendor/botble/platform/support/src/Http/Middleware/BaseMiddleware.php(12): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Support\\Http\\Middleware\\BaseMiddleware->handle()
#48 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#50 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#52 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#53 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#55 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#57 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#58 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#59 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#60 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#61 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#62 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#63 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#64 /home/<USER>/public_html/vendor/botble/platform/js-validation/src/RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#65 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\JsValidation\\RemoteValidationMiddleware->handle()
#66 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#67 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#68 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#69 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#70 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#71 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#72 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#73 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#74 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#75 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#76 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#77 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#78 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#79 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#80 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#81 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#82 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#83 /home/<USER>/public_html/public/index.php(24): Illuminate\\Foundation\\Http\\Kernel->handle()
#84 {main}
"} 
[2025-07-12 13:27:50] production.INFO: PayStation: Creating payment {"merchantId":"1389-1752255805","password":"bulbulee@567","invoice_number":"paystation_687262d601d9a","currency":"BDT","payment_amount":245,"pay_with_charge":1,"reference":"Order #paystation_687262d601d9a","cust_name":"Customer","cust_phone":"01700000000","cust_email":"<EMAIL>","cust_address":"Dhaka, Bangladesh","callback_url":"https://bulbulee.com/payments/paystation/callback","checkout_items":"{\"order_id\":\"paystation_687262d601d9a\",\"amount\":245,\"currency\":\"BDT\"}","opt_a":"Payment via PayStation","opt_b":"https://bulbulee.com","opt_c":"paystation_687262d601d9a"} 
[2025-07-12 13:27:55] production.INFO: PayStation API Response {"status_code":200,"response":{"status_code":"200","status":"success","message":"Payment Link Created Successfully.","payment_amount":245,"invoice_number":"paystation_687262d601d9a","payment_url":"https://api.paystation.com.bd/checkout/12117523268752920/fldg1VzazA1389MM1752327475"}} 
[2025-07-12 13:27:55] production.INFO: PayStation: Payment created successfully {"payment_url":"https://api.paystation.com.bd/checkout/12117523268752920/fldg1VzazA1389MM1752327475"} 
[2025-07-12 13:48:29] local.ERROR: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: delete from `activations` where `completed` = 0 and `created_at` < 2025-07-09 13:48:20) - C:\Users\<USER>\Desktop\uu\vendor\laravel\framework\src\Illuminate\Database\Connection.php:829  
