<?php

namespace Bo<PERSON>ble\PayStation\Http\Controllers;

use Botble\Base\Http\Controllers\BaseController;
use <PERSON><PERSON>ble\Ecommerce\Models\Order;
use Bo<PERSON>ble\Payment\Enums\PaymentStatusEnum;
use Botble\Payment\Supports\PaymentHelper;
use Botble\PayStation\Services\PayStationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PayStationController extends BaseController
{
    protected PayStationService $payStationService;

    public function __construct(PayStationService $payStationService)
    {
        $this->payStationService = $payStationService;
    }

    public function callback(Request $request)
    {
        Log::info('PayStation callback received', $request->all());

        try {
            $status = $request->get('status');
            $invoiceNumber = $request->get('invoice_number');
            $trxId = $request->get('trx_id');

            if (!$invoiceNumber) {
                Log::error('PayStation callback: Missing invoice number');
                return $this->redirectToCheckoutWithError('Invalid payment response');
            }

            // Get transaction status from PayStation API
            $transactionStatus = $this->payStationService->getTransactionStatus($invoiceNumber);

            if (!$transactionStatus['success']) {
                Log::error('PayStation callback: Failed to get transaction status', $transactionStatus);
                return $this->redirectToCheckoutWithError('Unable to verify payment status');
            }

            $transactionData = $transactionStatus['data'];
            $paymentStatus = strtolower($transactionData['trx_status']);

            Log::info('PayStation transaction status', [
                'invoice_number' => $invoiceNumber,
                'status' => $paymentStatus,
                'trx_id' => $transactionData['trx_id']
            ]);

            // Process payment based on status
            switch ($paymentStatus) {
                case 'success':
                    return $this->processSuccessfulPayment($transactionData);
                
                case 'failed':
                    return $this->processFailedPayment($transactionData);
                
                case 'processing':
                    return $this->processPendingPayment($transactionData);
                
                default:
                    Log::warning('PayStation callback: Unknown payment status', ['status' => $paymentStatus]);
                    return $this->redirectToCheckoutWithError('Unknown payment status');
            }

        } catch (\Exception $e) {
            Log::error('PayStation callback error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'request' => $request->all()
            ]);
            
            return $this->redirectToCheckoutWithError('Payment processing error');
        }
    }

    protected function processSuccessfulPayment(array $transactionData)
    {
        $invoiceNumber = $transactionData['invoice_number'];
        $trxId = $transactionData['trx_id'];

        Log::info('PayStation: Payment completed successfully', [
            'invoice_number' => $invoiceNumber,
            'trx_id' => $trxId
        ]);

        // Find the order by token (invoice_number)
        $order = Order::query()
            ->where('token', $invoiceNumber)
            ->first();

        if (!$order) {
            Log::error('PayStation: Order not found for invoice number: ' . $invoiceNumber);
            return redirect(PaymentHelper::getCancelURL($invoiceNumber))
                ->with('error_msg', 'Order not found');
        }

        // Use the proper payment processing action
        if (defined('PAYMENT_ACTION_PAYMENT_PROCESSED')) {
            do_action(PAYMENT_ACTION_PAYMENT_PROCESSED, [
                'amount' => $order->amount,
                'currency' => $order->currency ?? 'BDT',
                'charge_id' => $trxId,
                'order_id' => $order->id,
                'customer_id' => $order->user_id,
                'customer_type' => $order->user_id ? get_class($order->user) : null,
                'payment_channel' => PAYSTATION_PAYMENT_METHOD_NAME,
                'status' => PaymentStatusEnum::COMPLETED,
            ]);
        }

        // Redirect to the proper checkout success page
        return redirect(PaymentHelper::getRedirectURL($invoiceNumber));
    }

    protected function processFailedPayment(array $transactionData)
    {
        $invoiceNumber = $transactionData['invoice_number'];
        $errorMessage = $transactionData['error_message'] ?? 'Payment processing failed';

        Log::info('PayStation: Payment failed', [
            'invoice_number' => $invoiceNumber,
            'error_message' => $errorMessage
        ]);

        // Redirect to the cancel URL with error message
        return redirect(PaymentHelper::getCancelURL($invoiceNumber))
            ->with('error_msg', $errorMessage);
    }

    protected function processPendingPayment(array $transactionData)
    {
        $invoiceNumber = $transactionData['invoice_number'];

        Log::info('PayStation: Payment pending', [
            'invoice_number' => $invoiceNumber
        ]);

        return redirect()->route('payments.paystation.failed', [
            'invoice_number' => $invoiceNumber,
            'error_message' => 'Payment is being processed. Please wait for confirmation.'
        ]);
    }

    protected function redirectToCheckoutWithError(string $message)
    {
        return redirect(PaymentHelper::getCancelURL())
            ->with('error_msg', $message);
    }

    public function success(Request $request)
    {
        $invoiceNumber = $request->get('invoice_number');
        $transactionId = $request->get('trx_id');

        Log::info('PayStation success page accessed', [
            'invoice_number' => $invoiceNumber,
            'trx_id' => $transactionId
        ]);

        // Redirect to the proper checkout success page
        if ($invoiceNumber) {
            return redirect(PaymentHelper::getRedirectURL($invoiceNumber));
        }

        return redirect(PaymentHelper::getRedirectURL());
    }

    public function failed(Request $request)
    {
        $invoiceNumber = $request->get('invoice_number');
        $errorMessage = $request->get('error_message', 'Payment processing failed');

        Log::info('PayStation failed page accessed', [
            'invoice_number' => $invoiceNumber,
            'error_message' => $errorMessage
        ]);

        // Redirect to cancel URL with error message
        return redirect(PaymentHelper::getCancelURL($invoiceNumber))
            ->with('error_msg', $errorMessage);
    }

    public function cancel(Request $request)
    {
        $invoiceNumber = $request->get('invoice_number');

        Log::info('PayStation payment cancelled', ['invoice_number' => $invoiceNumber]);

        // Redirect to cancel URL with cancellation message
        return redirect(PaymentHelper::getCancelURL($invoiceNumber))
            ->with('error_msg', 'Payment was cancelled by user');
    }
}
