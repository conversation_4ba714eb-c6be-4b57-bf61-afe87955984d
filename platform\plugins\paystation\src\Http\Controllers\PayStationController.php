<?php

namespace Botble\PayStation\Http\Controllers;

use Botble\Base\Http\Controllers\BaseController;
use Bo<PERSON>ble\Payment\Enums\PaymentStatusEnum;
use Botble\Payment\Supports\PaymentHelper;
use Botble\PayStation\Services\PayStationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PayStationController extends BaseController
{
    protected PayStationService $payStationService;

    public function __construct(PayStationService $payStationService)
    {
        $this->payStationService = $payStationService;
    }

    public function callback(Request $request)
    {
        Log::info('PayStation callback received', $request->all());

        try {
            $status = $request->get('status');
            $invoiceNumber = $request->get('invoice_number');
            $trxId = $request->get('trx_id');

            if (!$invoiceNumber) {
                Log::error('PayStation callback: Missing invoice number');
                return $this->redirectToCheckoutWithError('Invalid payment response');
            }

            // Get transaction status from PayStation API
            $transactionStatus = $this->payStationService->getTransactionStatus($invoiceNumber);

            if (!$transactionStatus['success']) {
                Log::error('PayStation callback: Failed to get transaction status', $transactionStatus);
                return $this->redirectToCheckoutWithError('Unable to verify payment status');
            }

            $transactionData = $transactionStatus['data'];
            $paymentStatus = strtolower($transactionData['trx_status']);

            Log::info('PayStation transaction status', [
                'invoice_number' => $invoiceNumber,
                'status' => $paymentStatus,
                'trx_id' => $transactionData['trx_id']
            ]);

            // Process payment based on status
            switch ($paymentStatus) {
                case 'success':
                    return $this->processSuccessfulPayment($transactionData);
                
                case 'failed':
                    return $this->processFailedPayment($transactionData);
                
                case 'processing':
                    return $this->processPendingPayment($transactionData);
                
                default:
                    Log::warning('PayStation callback: Unknown payment status', ['status' => $paymentStatus]);
                    return $this->redirectToCheckoutWithError('Unknown payment status');
            }

        } catch (\Exception $e) {
            Log::error('PayStation callback error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'request' => $request->all()
            ]);
            
            return $this->redirectToCheckoutWithError('Payment processing error');
        }
    }

    protected function processSuccessfulPayment(array $transactionData)
    {
        $invoiceNumber = $transactionData['invoice_number'];
        
        // Find the payment record and update status
        $payment = get_payment_by_charge_id($invoiceNumber);
        
        if ($payment) {
            $payment->status = PaymentStatusEnum::COMPLETED;
            $payment->save();

            // Process the order
            PaymentHelper::storeLocalPayment([
                'amount' => $transactionData['payment_amount'],
                'currency' => 'BDT',
                'charge_id' => $invoiceNumber,
                'payment_channel' => PAYSTATION_PAYMENT_METHOD_NAME,
                'status' => PaymentStatusEnum::COMPLETED,
                'customer_id' => $payment->customer_id,
                'customer_type' => $payment->customer_type,
                'payment_type' => 'confirm',
                'order_id' => $payment->order_id,
            ]);

            Log::info('PayStation: Payment completed successfully', [
                'invoice_number' => $invoiceNumber,
                'trx_id' => $transactionData['trx_id']
            ]);

            return redirect()->route('public.checkout.success', $invoiceNumber);
        }

        Log::error('PayStation: Payment record not found', ['invoice_number' => $invoiceNumber]);
        return $this->redirectToCheckoutWithError('Payment record not found');
    }

    protected function processFailedPayment(array $transactionData)
    {
        $invoiceNumber = $transactionData['invoice_number'];
        
        Log::info('PayStation: Payment failed', [
            'invoice_number' => $invoiceNumber
        ]);

        return redirect()->route('public.checkout.information', $invoiceNumber)
            ->with('error_msg', 'Payment failed. Please try again.');
    }

    protected function processPendingPayment(array $transactionData)
    {
        $invoiceNumber = $transactionData['invoice_number'];
        
        Log::info('PayStation: Payment pending', [
            'invoice_number' => $invoiceNumber
        ]);

        return redirect()->route('public.checkout.information', $invoiceNumber)
            ->with('success_msg', 'Payment is being processed. You will be notified once completed.');
    }

    protected function redirectToCheckoutWithError(string $message)
    {
        return redirect()->route('public.checkout.information')
            ->with('error_msg', $message);
    }

    public function success(Request $request)
    {
        $invoiceNumber = $request->get('invoice_number');
        
        Log::info('PayStation success page accessed', ['invoice_number' => $invoiceNumber]);
        
        if ($invoiceNumber) {
            return redirect()->route('public.checkout.success', $invoiceNumber);
        }
        
        return redirect()->route('public.checkout.success');
    }

    public function cancel(Request $request)
    {
        $invoiceNumber = $request->get('invoice_number');
        
        Log::info('PayStation payment cancelled', ['invoice_number' => $invoiceNumber]);
        
        return redirect()->route('public.checkout.information')
            ->with('error_msg', 'Payment was cancelled. Please try again.');
    }
}
