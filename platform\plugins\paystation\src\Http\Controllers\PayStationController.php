<?php

namespace Bo<PERSON>ble\PayStation\Http\Controllers;

use Botble\Base\Http\Controllers\BaseController;
use <PERSON><PERSON>ble\Ecommerce\Models\Order;
use Bo<PERSON>ble\Payment\Enums\PaymentStatusEnum;
use Botble\Payment\Supports\PaymentHelper;
use Botble\PayStation\Services\PayStationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PayStationController extends BaseController
{
    protected PayStationService $payStationService;

    public function __construct(PayStationService $payStationService)
    {
        $this->payStationService = $payStationService;
    }

    public function callback(Request $request)
    {
        Log::info('PayStation callback received', $request->all());

        try {
            $status = $request->get('status');
            $invoiceNumber = $request->get('invoice_number');
            $trxId = $request->get('trx_id');

            if (!$invoiceNumber) {
                Log::error('PayStation callback: Missing invoice number');
                return $this->redirectToCheckoutWithError('Invalid payment response');
            }

            // If status is provided directly in callback, use it
            if ($status) {
                Log::info('PayStation: Using direct callback status', [
                    'invoice_number' => $invoiceNumber,
                    'direct_status' => $status,
                    'trx_id' => $trxId
                ]);

                $transactionData = [
                    'invoice_number' => $invoiceNumber,
                    'trx_id' => $trxId ?? uniqid('paystation_'),
                    'trx_status' => $status,
                    'amount' => $request->get('amount') ?? $request->get('payment_amount'),
                    'currency' => $request->get('currency') ?? 'BDT'
                ];

                return $this->processPaymentByStatus($transactionData);
            }

            // Get transaction status from PayStation API
            $transactionStatus = $this->payStationService->getTransactionStatus($invoiceNumber);

            if (!$transactionStatus['success']) {
                Log::error('PayStation callback: Failed to get transaction status', $transactionStatus);
                return $this->redirectToCheckoutWithError('Unable to verify payment status');
            }

            $transactionData = $transactionStatus['data'];
            $paymentStatus = strtolower($transactionData['trx_status'] ?? $transactionData['status'] ?? 'unknown');

            Log::info('PayStation transaction status', [
                'invoice_number' => $invoiceNumber,
                'status' => $paymentStatus,
                'trx_id' => $transactionData['trx_id'] ?? $transactionData['transaction_id'] ?? 'N/A',
                'full_response' => $transactionData
            ]);

            // Process payment based on status - handle multiple possible status values
            switch ($paymentStatus) {
                case 'success':
                case 'successful':
                case 'completed':
                case 'paid':
                case 'confirmed':
                    return $this->processSuccessfulPayment($transactionData);

                case 'failed':
                case 'failure':
                case 'declined':
                case 'rejected':
                case 'cancelled':
                case 'canceled':
                    return $this->processFailedPayment($transactionData);

                case 'processing':
                case 'pending':
                case 'initiated':
                case 'in_progress':
                    return $this->processPendingPayment($transactionData);

                default:
                    Log::warning('PayStation callback: Unknown payment status', [
                        'status' => $paymentStatus,
                        'full_data' => $transactionData
                    ]);
                    return $this->redirectToCheckoutWithError('Unknown payment status: ' . $paymentStatus);
            }

        } catch (\Exception $e) {
            Log::error('PayStation callback error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'request' => $request->all()
            ]);
            
            return $this->redirectToCheckoutWithError('Payment processing error');
        }
    }

    public function webhook(Request $request)
    {
        Log::info('PayStation webhook received', $request->all());

        try {
            // Handle webhook data directly
            $invoiceNumber = $request->input('invoice_number') ?? $request->input('invoice') ?? $request->input('order_id');
            $status = $request->input('status') ?? $request->input('trx_status') ?? $request->input('payment_status');
            $trxId = $request->input('trx_id') ?? $request->input('transaction_id') ?? $request->input('id');
            $amount = $request->input('amount') ?? $request->input('payment_amount');

            if (!$invoiceNumber) {
                Log::error('PayStation webhook: Missing invoice number');
                return response()->json(['status' => 'error', 'message' => 'Missing invoice number'], 400);
            }

            if (!$status) {
                Log::error('PayStation webhook: Missing payment status');
                return response()->json(['status' => 'error', 'message' => 'Missing payment status'], 400);
            }

            $transactionData = [
                'invoice_number' => $invoiceNumber,
                'trx_id' => $trxId ?? uniqid('paystation_'),
                'trx_status' => $status,
                'amount' => $amount,
                'currency' => $request->input('currency') ?? 'BDT'
            ];

            Log::info('PayStation webhook processing', $transactionData);

            $this->processPaymentByStatus($transactionData);

            return response()->json(['status' => 'success', 'message' => 'Webhook processed']);

        } catch (\Exception $e) {
            Log::error('PayStation webhook error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'request' => $request->all()
            ]);

            return response()->json(['status' => 'error', 'message' => 'Webhook processing failed'], 500);
        }
    }

    protected function processPaymentByStatus(array $transactionData)
    {
        $paymentStatus = strtolower($transactionData['trx_status'] ?? $transactionData['status'] ?? 'unknown');

        Log::info('PayStation processing payment by status', [
            'status' => $paymentStatus,
            'data' => $transactionData
        ]);

        // Process payment based on status - handle multiple possible status values
        switch ($paymentStatus) {
            case 'success':
            case 'successful':
            case 'completed':
            case 'paid':
            case 'confirmed':
                return $this->processSuccessfulPayment($transactionData);

            case 'failed':
            case 'failure':
            case 'declined':
            case 'rejected':
            case 'cancelled':
            case 'canceled':
                return $this->processFailedPayment($transactionData);

            case 'processing':
            case 'pending':
            case 'initiated':
            case 'in_progress':
                return $this->processPendingPayment($transactionData);

            default:
                Log::warning('PayStation: Unknown payment status in direct callback', [
                    'status' => $paymentStatus,
                    'full_data' => $transactionData
                ]);
                return $this->redirectToCheckoutWithError('Unknown payment status: ' . $paymentStatus);
        }
    }

    protected function processSuccessfulPayment(array $transactionData)
    {
        $invoiceNumber = $transactionData['invoice_number'] ?? $transactionData['invoice'] ?? null;
        $trxId = $transactionData['trx_id'] ?? $transactionData['transaction_id'] ?? $transactionData['id'] ?? uniqid('paystation_');

        Log::info('PayStation: Payment completed successfully', [
            'invoice_number' => $invoiceNumber,
            'trx_id' => $trxId,
            'full_data' => $transactionData
        ]);

        // Find the order by token (invoice_number)
        $order = Order::query()
            ->where('token', $invoiceNumber)
            ->first();

        if (!$order) {
            Log::error('PayStation: Order not found for invoice number: ' . $invoiceNumber, [
                'searched_token' => $invoiceNumber,
                'available_orders' => Order::query()->select('id', 'token', 'code')->limit(10)->get()->toArray()
            ]);
            return redirect(PaymentHelper::getCancelURL($invoiceNumber))
                ->with('error_msg', 'Order not found');
        }

        Log::info('PayStation: Order found, processing payment', [
            'order_id' => $order->id,
            'order_amount' => $order->amount,
            'order_status' => $order->status
        ]);

        // Use the proper payment processing action
        if (defined('PAYMENT_ACTION_PAYMENT_PROCESSED')) {
            $paymentData = [
                'amount' => $order->amount,
                'currency' => $order->currency ?? 'BDT',
                'charge_id' => $trxId,
                'order_id' => $order->id,
                'customer_id' => $order->user_id,
                'customer_type' => $order->user_id ? get_class($order->user) : null,
                'payment_channel' => PAYSTATION_PAYMENT_METHOD_NAME,
                'status' => PaymentStatusEnum::COMPLETED,
            ];

            Log::info('PayStation: Triggering PAYMENT_ACTION_PAYMENT_PROCESSED', $paymentData);

            do_action(PAYMENT_ACTION_PAYMENT_PROCESSED, $paymentData);

            Log::info('PayStation: PAYMENT_ACTION_PAYMENT_PROCESSED completed');
        } else {
            Log::error('PayStation: PAYMENT_ACTION_PAYMENT_PROCESSED not defined');

            // Fallback: Create payment record directly
            $payment = PaymentHelper::storeLocalPayment([
                'amount' => $order->amount,
                'currency' => $order->currency ?? 'BDT',
                'charge_id' => $trxId,
                'order_id' => $order->id,
                'customer_id' => $order->user_id,
                'customer_type' => $order->user_id ? get_class($order->user) : null,
                'payment_channel' => PAYSTATION_PAYMENT_METHOD_NAME,
                'status' => PaymentStatusEnum::COMPLETED,
            ]);

            if ($payment) {
                $order->payment_id = $payment->id;
                $order->save();
                Log::info('PayStation: Payment record created directly', ['payment_id' => $payment->id]);
            }
        }

        // Redirect to the proper checkout success page
        return redirect(PaymentHelper::getRedirectURL($invoiceNumber));
    }

    protected function processFailedPayment(array $transactionData)
    {
        $invoiceNumber = $transactionData['invoice_number'];
        $errorMessage = $transactionData['error_message'] ?? 'Payment processing failed';

        Log::info('PayStation: Payment failed', [
            'invoice_number' => $invoiceNumber,
            'error_message' => $errorMessage
        ]);

        // Redirect to the cancel URL with error message
        return redirect(PaymentHelper::getCancelURL($invoiceNumber))
            ->with('error_msg', $errorMessage);
    }

    protected function processPendingPayment(array $transactionData)
    {
        $invoiceNumber = $transactionData['invoice_number'];

        Log::info('PayStation: Payment pending', [
            'invoice_number' => $invoiceNumber
        ]);

        return redirect()->route('payments.paystation.failed', [
            'invoice_number' => $invoiceNumber,
            'error_message' => 'Payment is being processed. Please wait for confirmation.'
        ]);
    }

    protected function redirectToCheckoutWithError(string $message)
    {
        return redirect(PaymentHelper::getCancelURL())
            ->with('error_msg', $message);
    }

    public function success(Request $request)
    {
        $invoiceNumber = $request->get('invoice_number');
        $transactionId = $request->get('trx_id');

        Log::info('PayStation success page accessed', [
            'invoice_number' => $invoiceNumber,
            'trx_id' => $transactionId
        ]);

        // Redirect to the proper checkout success page
        if ($invoiceNumber) {
            return redirect(PaymentHelper::getRedirectURL($invoiceNumber));
        }

        return redirect(PaymentHelper::getRedirectURL());
    }

    public function failed(Request $request)
    {
        $invoiceNumber = $request->get('invoice_number');
        $errorMessage = $request->get('error_message', 'Payment processing failed');

        Log::info('PayStation failed page accessed', [
            'invoice_number' => $invoiceNumber,
            'error_message' => $errorMessage
        ]);

        // Redirect to cancel URL with error message
        return redirect(PaymentHelper::getCancelURL($invoiceNumber))
            ->with('error_msg', $errorMessage);
    }

    public function cancel(Request $request)
    {
        $invoiceNumber = $request->get('invoice_number');

        Log::info('PayStation payment cancelled', ['invoice_number' => $invoiceNumber]);

        // Redirect to cancel URL with cancellation message
        return redirect(PaymentHelper::getCancelURL($invoiceNumber))
            ->with('error_msg', 'Payment was cancelled by user');
    }

    public function testCallback(Request $request)
    {
        // Test method to simulate a successful payment callback
        $testData = [
            'invoice_number' => $request->get('invoice', 'test_invoice_123'),
            'trx_id' => 'test_trx_' . time(),
            'trx_status' => 'success',
            'amount' => 100,
            'currency' => 'BDT'
        ];

        Log::info('PayStation: Test callback triggered', $testData);

        try {
            $result = $this->processPaymentByStatus($testData);
            return response()->json([
                'status' => 'success',
                'message' => 'Test callback processed',
                'data' => $testData,
                'result' => 'Redirect would happen here'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
                'data' => $testData
            ]);
        }
    }
}
