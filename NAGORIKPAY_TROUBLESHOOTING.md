# NagorikPay Plugin Troubleshooting Guide

## Issue 1: Settings Not Saving in Admin Panel

### Symptoms:
- Click "Activate" in Payment Methods but it doesn't save
- Settings revert to previous values after saving

### Solutions:

#### Solution A: Check Form Structure
The settings form might not be using the correct structure. Try this:

1. Go to `platform/plugins/nagorikpay/resources/views/settings.blade.php`
2. Make sure the form fields have the correct names:
   - `payment_nagorikpay_status`
   - `payment_nagorikpay_name`
   - `payment_nagorikpay_description`
   - `payment_nagorikpay_api_key`
   - `payment_nagorikpay_mode`

#### Solution B: Clear Cache
```bash
php artisan cache:clear
php artisan config:clear
php artisan view:clear
```

#### Solution C: Check File Permissions
Make sure the storage directory is writable:
```bash
chmod -R 775 storage/
chmod -R 775 bootstrap/cache/
```

## Issue 2: 500 Error on Checkout Page

### Symptoms:
- Checkout page shows 500 Internal Server Error
- Error occurs when NagorikPay is selected as payment method

### Solutions:

#### Solution A: Check API Key Configuration
1. Go to Admin Panel → Settings → Payment Methods
2. Make sure API Key is set: `gnXi7etgWNhFyFGZFrOMYyrmnF4A1eGU5SC2QRmUvILOlNc2Ef`
3. Set Mode to "Sandbox" for testing

#### Solution B: Check Constants
Run the debug script:
```bash
php debug-nagorikpay.php
```

#### Solution C: Check Service Dependencies
The error might be due to missing dependencies. Check if these are properly loaded:
- `Illuminate\Support\Facades\Http`
- `Botble\Payment\Models\Payment`
- `Botble\Payment\Enums\PaymentStatusEnum`

#### Solution D: Check Routes
Make sure these routes are accessible:
- `/payments/nagorikpay/success`
- `/payments/nagorikpay/cancel`
- `/payments/nagorikpay/webhook`

## Issue 3: Plugin Not Showing in Payment Methods

### Symptoms:
- Plugin is activated but doesn't appear in checkout
- NagorikPay option missing from payment methods list

### Solutions:

#### Solution A: Check Plugin Status
1. Go to Admin Panel → Plugins
2. Make sure NagorikPay is activated
3. Check if Payment plugin is also activated

#### Solution B: Check Settings
1. Go to Admin Panel → Settings → Payment Methods
2. Find NagorikPay section
3. Set Status to "Yes"
4. Save settings

#### Solution C: Clear Cache and Refresh
```bash
php artisan cache:clear
php artisan view:clear
php artisan route:clear
```

## Issue 4: Payment Processing Errors

### Symptoms:
- Payment fails with API errors
- "Payment service temporarily unavailable" message

### Solutions:

#### Solution A: Verify API Key
Make sure you're using the correct API key:
```
gnXi7etgWNhFyFGZFrOMYyrmnF4A1eGU5SC2QRmUvILOlNc2Ef
```

#### Solution B: Check Network Connectivity
Test if your server can reach NagorikPay API:
```bash
curl -X POST https://secure-pay.nagorikpay.com/api/payment/create \
  -H "API-KEY: gnXi7etgWNhFyFGZFrOMYyrmnF4A1eGU5SC2QRmUvILOlNc2Ef" \
  -H "Content-Type: application/json" \
  -d '{"amount":"10","success_url":"test","cancel_url":"test","webhook_url":"test"}'
```

#### Solution C: Check SSL/HTTPS
Make sure your site is running on HTTPS for production use.

## Issue 5: Webhook Not Working

### Symptoms:
- Payment status not updating automatically
- Orders stuck in pending status

### Solutions:

#### Solution A: Check Webhook URL
Make sure this URL is accessible from external servers:
```
https://yourdomain.com/payments/nagorikpay/webhook
```

#### Solution B: Test Webhook Manually
Send a test POST request to your webhook URL:
```bash
curl -X POST https://yourdomain.com/payments/nagorikpay/webhook \
  -H "Content-Type: application/json" \
  -d '{"transaction_id":"TEST123","status":"COMPLETED"}'
```

## Debug Commands

### Run Debug Script
```bash
php debug-nagorikpay.php
```

### Check Laravel Logs
```bash
tail -f storage/logs/laravel.log
```

### Test Plugin Service
```php
// In tinker: php artisan tinker
$service = app(\Botble\NagorikPay\Services\NagorikPayService::class);
// Should not throw errors
```

## Common Error Messages and Solutions

### "NagorikPay API key is not configured"
- Set the API key in admin panel settings

### "Payment service temporarily unavailable"
- Check internet connection
- Verify API key is correct
- Check if NagorikPay service is online

### "Class not found" errors
- Clear cache: `php artisan cache:clear`
- Run: `composer dump-autoload`

### "Route not found" errors
- Clear route cache: `php artisan route:clear`
- Check if routes are properly registered

## Contact Support

If none of these solutions work:

1. **Check Laravel Logs**: Look in `storage/logs/laravel.log` for detailed error messages
2. **Enable Debug Mode**: Set `APP_DEBUG=true` in `.env` file temporarily
3. **Run Debug Script**: Use `php debug-nagorikpay.php` to get system information
4. **Contact Developer**: Provide the debug output and error logs

## Quick Fix Checklist

- [ ] Plugin is activated
- [ ] Payment plugin is activated
- [ ] API key is set correctly
- [ ] Settings are saved (Status = Yes)
- [ ] Cache is cleared
- [ ] Routes are accessible
- [ ] HTTPS is enabled (for production)
- [ ] File permissions are correct
