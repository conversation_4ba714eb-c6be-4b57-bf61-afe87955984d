<?php

namespace Bo<PERSON>ble\NagorikPay\Providers;

use Bo<PERSON>ble\Base\Supports\ServiceProvider;
use Bo<PERSON>ble\Payment\Enums\PaymentStatusEnum;
use Bo<PERSON>ble\Payment\Models\Payment;
use Illuminate\Http\Request;

class HookServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        add_filter(PAYMENT_FILTER_PAYMENT_PARAMETERS, [$this, 'checkoutWithNagorikPay'], 16, 2);
    }

    public function checkoutWithNagorikPay(?array $paymentData, Request $request): array
    {
        if ($request->input('payment_method') !== NAGORIKPAY_PAYMENT_METHOD_NAME) {
            return $paymentData;
        }

        $currentCurrency = get_application_currency();

        $paymentData['currency'] = $currentCurrency->title;
        $paymentData['currency_symbol'] = $currentCurrency->symbol;
        $paymentData['exchange_rate'] = $currentCurrency->exchange_rate;

        return $paymentData;
    }
}
