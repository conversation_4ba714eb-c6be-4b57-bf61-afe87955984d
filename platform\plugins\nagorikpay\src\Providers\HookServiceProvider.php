<?php

namespace Bo<PERSON>ble\NagorikPay\Providers;

use Bo<PERSON>ble\Payment\Facades\PaymentMethods;
use Bo<PERSON>ble\NagorikPay\Forms\NagorikPaymentMethodForm;
use Illuminate\Http\Request;
use Illuminate\Support\ServiceProvider;

class HookServiceProvider extends ServiceProvider
{
    private static $registered = false;

    public function boot(): void
    {
        // Prevent multiple registrations
        if (self::$registered) {
            return;
        }
        self::$registered = true;

        // Simple method registration without complex hooks
        add_filter(PAYMENT_FILTER_ADDITIONAL_PAYMENT_METHODS, [$this, 'registerNagorikPayMethod'], 16, 2);
        add_filter(PAYMENT_METHODS_SETTINGS_PAGE, [$this, 'addPaymentSettings'], 16);

        // Only add checkout hook if not in admin
        if (!is_admin()) {
            add_filter(PAYMENT_FILTER_AFTER_POST_CHECKOUT, [$this, 'checkoutWithNagorikPay'], 16, 2);
        }
    }

    public function registerNagorikPayMethod(?string $html, array $data): string
    {
        try {
            if (get_payment_setting('status', NAGORIKPAY_PAYMENT_METHOD_NAME) == 1) {
                PaymentMethods::method(NAGORIKPAY_PAYMENT_METHOD_NAME, [
                    'html' => view('plugins/nagorikpay::methods', $data)->render(),
                ]);
            }
        } catch (\Exception $e) {
            \Log::error('NagorikPay method registration error: ' . $e->getMessage());
        }

        return $html;
    }

    public function addPaymentSettings(?string $settings): string
    {
        try {
            return $settings . NagorikPaymentMethodForm::create()->renderForm();
        } catch (\Exception $e) {
            \Log::error('NagorikPay settings form error: ' . $e->getMessage());
            return $settings;
        }
    }

    public function checkoutWithNagorikPay(array $data, Request $request): array
    {
        // Prevent recursive calls
        static $processing = false;
        if ($processing) {
            return $data;
        }

        try {
            if ($request->input('payment_method') === NAGORIKPAY_PAYMENT_METHOD_NAME && $request->isMethod('POST')) {
                $processing = true;
                
                // Simple redirect to NagorikPay (like WHMCS)
                $apiKey = get_payment_setting('api_key', NAGORIKPAY_PAYMENT_METHOD_NAME);
                if (!empty($apiKey)) {
                    $data['checkoutUrl'] = 'https://secure-pay.nagorikpay.com/demo-payment';
                } else {
                    $data['error'] = true;
                    $data['message'] = 'NagorikPay API key not configured';
                }
                
                $processing = false;
            }
        } catch (\Exception $e) {
            $processing = false;
            \Log::error('NagorikPay checkout error: ' . $e->getMessage());
            $data['error'] = true;
            $data['message'] = 'Payment service error';
        }

        return $data;
    }
}
