<?php

namespace Bo<PERSON><PERSON>\NagorikPay\Providers;

use Bo<PERSON>ble\Base\Facades\Html;
use Bo<PERSON>ble\Payment\Enums\PaymentMethodEnum;
use Bo<PERSON>ble\Payment\Facades\PaymentMethods;
use Botble\NagorikPay\Services\NagorikPayService;
use Botble\NagorikPay\Forms\NagorikPaymentMethodForm;
use Illuminate\Http\Request;
use Illuminate\Support\ServiceProvider;

class HookServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        add_filter(PAYMENT_FILTER_ADDITIONAL_PAYMENT_METHODS, [$this, 'registerNagorikPayMethod'], 2, 2);

        $this->app->booted(function () {
            add_filter(PAYMENT_FILTER_AFTER_POST_CHECKOUT, [$this, 'checkoutWithNagorikPay'], 2, 2);
        });

        add_filter(PAYMENT_METHODS_SETTINGS_PAGE, [$this, 'addPaymentSettings'], 2);

        add_filter(BASE_FILTER_ENUM_ARRAY, function ($values, $class) {
            if ($class == PaymentMethodEnum::class) {
                $values['NAGORIKPAY'] = NAGORIKPAY_PAYMENT_METHOD_NAME;
            }
            return $values;
        }, 2, 2);

        add_filter(BASE_FILTER_ENUM_LABEL, function ($value, $class) {
            if ($class == PaymentMethodEnum::class && $value == NAGORIKPAY_PAYMENT_METHOD_NAME) {
                $value = 'NagorikPay';
            }
            return $value;
        }, 2, 2);

        add_filter(BASE_FILTER_ENUM_HTML, function ($value, $class) {
            if ($class == PaymentMethodEnum::class && $value == NAGORIKPAY_PAYMENT_METHOD_NAME) {
                $value = Html::tag(
                    'span',
                    PaymentMethodEnum::getLabel($value),
                    ['class' => 'label-success status-label']
                )->toHtml();
            }
            return $value;
        }, 2, 2);

        add_filter(PAYMENT_FILTER_GET_SERVICE_CLASS, function ($data, $value) {
            if ($value == NAGORIKPAY_PAYMENT_METHOD_NAME) {
                $data = NagorikPayService::class;
            }
            return $data;
        }, 2, 2);
    }

    public function addPaymentSettings(?string $settings): string
    {
        return $settings . NagorikPaymentMethodForm::create()->renderForm();
    }

    public function registerNagorikPayMethod(?string $html, array $data): string
    {
        PaymentMethods::method(NAGORIKPAY_PAYMENT_METHOD_NAME, [
            'html' => view('plugins/nagorikpay::methods', $data)->render(),
        ]);

        return $html;
    }

    public function checkoutWithNagorikPay(array $data, Request $request): array
    {
        if ($request->input('payment_method') == NAGORIKPAY_PAYMENT_METHOD_NAME) {
            $currentCurrency = get_application_currency();

            $nagorikPayService = $this->app->make(NagorikPayService::class);

            $supportedCurrencies = $nagorikPayService->supportedCurrencyCodes();
            $currency = strtoupper($currentCurrency->title);

            if (!in_array($currency, $supportedCurrencies)) {
                $data['error'] = true;
                $data['message'] = __(":name doesn't support :currency. List of currencies supported by :name: :currencies.", [
                    'name' => 'NagorikPay',
                    'currency' => $currency,
                    'currencies' => implode(', ', $supportedCurrencies),
                ]);
                return $data;
            }

            $paymentData = apply_filters(PAYMENT_FILTER_PAYMENT_DATA, [], $request);

            $checkoutUrl = $nagorikPayService->makePayment($paymentData);

            if (is_array($checkoutUrl) && isset($checkoutUrl['error']) && $checkoutUrl['error']) {
                $data['error'] = true;
                $data['message'] = $checkoutUrl['message'] ?? __('Something went wrong. Please try again later.');
            } else {
                if (is_string($checkoutUrl)) {
                    $data['checkoutUrl'] = $checkoutUrl;
                } else {
                    $data['error'] = true;
                    $data['message'] = __('Something went wrong. Please try again later.');
                }
            }

            return $data;
        }

        return $data;
    }
}
