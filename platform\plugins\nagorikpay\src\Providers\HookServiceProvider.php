<?php

namespace Bo<PERSON>ble\NagorikPay\Providers;

use Bo<PERSON>ble\Base\Facades\Html;
use Bo<PERSON>ble\Payment\Enums\PaymentMethodEnum;
use Bo<PERSON>ble\Payment\Facades\PaymentMethods;
use Botble\NagorikPay\Services\NagorikPayService;
use Illuminate\Http\Request;
use Illuminate\Support\ServiceProvider;

class HookServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        add_filter(PAYMENT_FILTER_ADDITIONAL_PAYMENT_METHODS, [$this, 'registerNagorikPayMethod'], 2, 2);

        $this->app->booted(function () {
            add_filter(PAYMENT_FILTER_AFTER_POST_CHECKOUT, [$this, 'checkoutWithNagorikPay'], 2, 2);
        });

        add_filter(PAYMENT_METHODS_SETTINGS_PAGE, [$this, 'addPaymentSettings'], 2);

        add_filter(BASE_FILTER_ENUM_ARRAY, function ($values, $class) {
            try {
                if ($class == PaymentMethodEnum::class) {
                    $methodName = defined('NAGORIKPAY_PAYMENT_METHOD_NAME') ? NAGORIKPAY_PAYMENT_METHOD_NAME : 'nagorikpay';
                    $values['NAGORIKPAY'] = $methodName;
                }
            } catch (\Exception $e) {
                \Log::error('NagorikPay enum array error: ' . $e->getMessage());
            }
            return $values;
        }, 2, 2);

        add_filter(BASE_FILTER_ENUM_LABEL, function ($value, $class) {
            try {
                $methodName = defined('NAGORIKPAY_PAYMENT_METHOD_NAME') ? NAGORIKPAY_PAYMENT_METHOD_NAME : 'nagorikpay';
                if ($class == PaymentMethodEnum::class && $value == $methodName) {
                    $value = 'NagorikPay';
                }
            } catch (\Exception $e) {
                \Log::error('NagorikPay enum label error: ' . $e->getMessage());
            }
            return $value;
        }, 2, 2);

        add_filter(BASE_FILTER_ENUM_HTML, function ($value, $class) {
            try {
                $methodName = defined('NAGORIKPAY_PAYMENT_METHOD_NAME') ? NAGORIKPAY_PAYMENT_METHOD_NAME : 'nagorikpay';
                if ($class == PaymentMethodEnum::class && $value == $methodName) {
                    $value = Html::tag(
                        'span',
                        'NagorikPay',
                        ['class' => 'label-success status-label']
                    )->toHtml();
                }
            } catch (\Exception $e) {
                \Log::error('NagorikPay enum HTML error: ' . $e->getMessage());
            }
            return $value;
        }, 2, 2);

        add_filter(PAYMENT_FILTER_GET_SERVICE_CLASS, function ($data, $value) {
            try {
                $methodName = defined('NAGORIKPAY_PAYMENT_METHOD_NAME') ? NAGORIKPAY_PAYMENT_METHOD_NAME : 'nagorikpay';
                if ($value == $methodName) {
                    $data = NagorikPayService::class;
                }
            } catch (\Exception $e) {
                \Log::error('NagorikPay service class error: ' . $e->getMessage());
            }
            return $data;
        }, 2, 2);
    }

    public function addPaymentSettings(?string $settings): string
    {
        try {
            return $settings . view('plugins/nagorikpay::settings')->render();
        } catch (\Exception $e) {
            \Log::error('NagorikPay settings form error: ' . $e->getMessage());
            return $settings;
        }
    }

    public function registerNagorikPayMethod(?string $html, array $data): string
    {
        try {
            $methodName = defined('NAGORIKPAY_PAYMENT_METHOD_NAME') ? NAGORIKPAY_PAYMENT_METHOD_NAME : 'nagorikpay';

            PaymentMethods::method($methodName, [
                'html' => view('plugins/nagorikpay::methods', $data)->render(),
            ]);
        } catch (\Exception $e) {
            \Log::error('NagorikPay method registration error: ' . $e->getMessage());
        }

        return $html;
    }

    public function checkoutWithNagorikPay(array $data, Request $request): array
    {
        try {
            $methodName = defined('NAGORIKPAY_PAYMENT_METHOD_NAME') ? NAGORIKPAY_PAYMENT_METHOD_NAME : 'nagorikpay';

            if ($request->input('payment_method') == $methodName) {
                $currentCurrency = get_application_currency();

                $nagorikPayService = $this->app->make(NagorikPayService::class);

                $supportedCurrencies = $nagorikPayService->supportedCurrencyCodes();
                $currency = strtoupper($currentCurrency->title);

                if (!in_array($currency, $supportedCurrencies)) {
                    $data['error'] = true;
                    $data['message'] = "NagorikPay doesn't support {$currency}. Supported currencies: " . implode(', ', $supportedCurrencies);
                    return $data;
                }

                $paymentData = apply_filters(PAYMENT_FILTER_PAYMENT_DATA, [], $request);

                $checkoutUrl = $nagorikPayService->makePayment($paymentData);

                if (is_array($checkoutUrl) && isset($checkoutUrl['error']) && $checkoutUrl['error']) {
                    $data['error'] = true;
                    $data['message'] = $checkoutUrl['message'] ?? 'Something went wrong. Please try again later.';
                } else {
                    if (is_string($checkoutUrl)) {
                        $data['checkoutUrl'] = $checkoutUrl;
                    } else {
                        $data['error'] = true;
                        $data['message'] = 'Something went wrong. Please try again later.';
                    }
                }

                return $data;
            }
        } catch (\Exception $e) {
            \Log::error('NagorikPay checkout error: ' . $e->getMessage());
            $data['error'] = true;
            $data['message'] = 'Payment service temporarily unavailable.';
        }

        return $data;
    }
}
