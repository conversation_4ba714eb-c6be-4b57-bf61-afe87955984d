<?php
// Simple Database Admin Panel
session_start();

// Database configuration
$host = '127.0.0.1';
$port = '4409';
$dbname = 'naru';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

// Handle SQL execution
$result = null;
$error = null;
$success = null;

if ($_POST['sql'] ?? false) {
    $sql = trim($_POST['sql']);
    try {
        if (stripos($sql, 'SELECT') === 0 || stripos($sql, 'SHOW') === 0 || stripos($sql, 'DESCRIBE') === 0) {
            $stmt = $pdo->query($sql);
            $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
        } else {
            $affected = $pdo->exec($sql);
            $success = "Query executed successfully. Affected rows: $affected";
        }
    } catch(PDOException $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Get tables
$tables = [];
try {
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
} catch(PDOException $e) {
    $error = "Error fetching tables: " . $e->getMessage();
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Database Admin Panel</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .sidebar { float: left; width: 200px; margin-right: 20px; }
        .main { margin-left: 220px; }
        .table-list { background: #f5f5f5; padding: 10px; border-radius: 5px; }
        .table-list h3 { margin-top: 0; }
        .table-list a { display: block; padding: 5px; text-decoration: none; color: #333; }
        .table-list a:hover { background: #ddd; }
        textarea { width: 100%; height: 150px; font-family: monospace; }
        table { border-collapse: collapse; width: 100%; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .error { color: red; background: #ffe6e6; padding: 10px; border-radius: 5px; }
        .success { color: green; background: #e6ffe6; padding: 10px; border-radius: 5px; }
        .btn { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        .btn:hover { background: #005a87; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Database Admin Panel - <?php echo $dbname; ?></h1>
        
        <div class="sidebar">
            <div class="table-list">
                <h3>Tables (<?php echo count($tables); ?>)</h3>
                <?php foreach($tables as $table): ?>
                    <a href="?table=<?php echo urlencode($table); ?>"><?php echo htmlspecialchars($table); ?></a>
                <?php endforeach; ?>
            </div>
        </div>
        
        <div class="main">
            <?php if($error): ?>
                <div class="error"><?php echo htmlspecialchars($error); ?></div>
            <?php endif; ?>
            
            <?php if($success): ?>
                <div class="success"><?php echo htmlspecialchars($success); ?></div>
            <?php endif; ?>
            
            <form method="post">
                <h3>Execute SQL Query</h3>
                <textarea name="sql" placeholder="Enter your SQL query here..."><?php echo htmlspecialchars($_POST['sql'] ?? ''); ?></textarea>
                <br><br>
                <button type="submit" class="btn">Execute Query</button>
            </form>
            
            <?php if(isset($_GET['table'])): ?>
                <?php
                $table = $_GET['table'];
                try {
                    $stmt = $pdo->query("SELECT * FROM `$table` LIMIT 100");
                    $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    echo "<h3>Table: $table (showing first 100 rows)</h3>";
                    if($data) {
                        echo "<table>";
                        echo "<tr>";
                        foreach(array_keys($data[0]) as $column) {
                            echo "<th>" . htmlspecialchars($column) . "</th>";
                        }
                        echo "</tr>";
                        foreach($data as $row) {
                            echo "<tr>";
                            foreach($row as $value) {
                                echo "<td>" . htmlspecialchars($value) . "</td>";
                            }
                            echo "</tr>";
                        }
                        echo "</table>";
                    } else {
                        echo "<p>No data found in table.</p>";
                    }
                } catch(PDOException $e) {
                    echo "<div class='error'>Error: " . htmlspecialchars($e->getMessage()) . "</div>";
                }
                ?>
            <?php endif; ?>
            
            <?php if($result): ?>
                <h3>Query Results</h3>
                <table>
                    <tr>
                        <?php foreach(array_keys($result[0]) as $column): ?>
                            <th><?php echo htmlspecialchars($column); ?></th>
                        <?php endforeach; ?>
                    </tr>
                    <?php foreach($result as $row): ?>
                        <tr>
                            <?php foreach($row as $value): ?>
                                <td><?php echo htmlspecialchars($value); ?></td>
                            <?php endforeach; ?>
                        </tr>
                    <?php endforeach; ?>
                </table>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
