<?php

namespace Bo<PERSON><PERSON>\Ecommerce\Listeners;

use Bo<PERSON>ble\Ecommerce\Events\OrderCreated;
use Bo<PERSON>ble\Ecommerce\Events\OrderPlacedEvent;
use Bo<PERSON>ble\Ecommerce\Facades\InvoiceHelper;
use Illuminate\Contracts\Queue\ShouldQueue;

class GenerateInvoiceListener implements ShouldQueue
{
    public function handle(OrderPlacedEvent|OrderCreated $event): void
    {
        $order = $event->order;

        InvoiceHelper::store($order);
    }
}
