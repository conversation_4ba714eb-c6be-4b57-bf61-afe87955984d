<?php

// Bootstrap Laravel
require __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Botble\Setting\Facades\Setting;

echo "=== NagorikPay Plugin Setup ===\n\n";

// Configure NagorikPay settings
$settings = [
    'payment_nagorikpay_name' => 'NagorikPay',
    'payment_nagorikpay_description' => 'Pay securely with NagorikPay - supports bKash, Nagad, Rocket and other popular payment methods in Bangladesh',
    'payment_nagorikpay_status' => '1', // Enable by default
    'payment_nagorikpay_mode' => 'sandbox', // Start with sandbox
    'payment_nagorikpay_api_key' => 'k19yyA1dYDg7zxmfhaDhHgRrYyrxNXI2G10wQUWbwLzAeE2ZFD',
];

echo "Configuring NagorikPay settings...\n";

foreach ($settings as $key => $value) {
    Setting::set($key, $value);
    echo "- $key: $value\n";
}

Setting::save();

echo "\n✅ NagorikPay plugin configured successfully!\n\n";

echo "Settings applied:\n";
echo "- Status: ENABLED\n";
echo "- Name: NagorikPay\n";
echo "- Mode: Sandbox (for testing)\n";
echo "- API Key: k19yyA1dYDg7zxmfhaDhHgRrYyrxNXI2G10wQUWbwLzAeE2ZFD\n";

echo "\nNext steps:\n";
echo "1. Go to your admin panel\n";
echo "2. Navigate to Settings → Payment Methods\n";
echo "3. Verify NagorikPay settings are correct\n";
echo "4. Test with a sample purchase\n";
echo "5. When ready for production, change mode to 'live'\n";

echo "\n=== Setup Complete ===\n";
