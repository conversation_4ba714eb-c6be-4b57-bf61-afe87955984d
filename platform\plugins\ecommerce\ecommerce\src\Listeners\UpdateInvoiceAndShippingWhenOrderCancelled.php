<?php

namespace Bo<PERSON><PERSON>\Ecommerce\Listeners;

use Bo<PERSON>ble\Ecommerce\Enums\InvoiceStatusEnum;
use Bo<PERSON>ble\Ecommerce\Enums\ShippingStatusEnum;
use Botble\Ecommerce\Events\OrderCancelledEvent;
use Illuminate\Contracts\Queue\ShouldQueue;

class UpdateInvoiceAndShippingWhenOrderCancelled implements ShouldQueue
{
    public function handle(OrderCancelledEvent $event): void
    {
        $event->order->invoice()->update(['status' => InvoiceStatusEnum::CANCELED]);
        $event->order->shipment()->update(['status' => ShippingStatusEnum::CANCELED]);
    }
}
