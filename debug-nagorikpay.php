<?php

// Bootstrap Laravel
require __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== NagorikPay Plugin Debug ===\n\n";

// Check if plugin is active
$activePlugins = get_active_plugins();
$isActive = in_array('nagorikpay', $activePlugins);
echo "Plugin Active: " . ($isActive ? 'YES' : 'NO') . "\n";

if (!$isActive) {
    echo "Plugin is not active. Please activate it first.\n";
    exit;
}

// Check constants
echo "Constants defined: " . (defined('NAGORIKPAY_PAYMENT_METHOD_NAME') ? 'YES' : 'NO') . "\n";
if (defined('NAGORIKPAY_PAYMENT_METHOD_NAME')) {
    echo "Method name: " . NAGORIKPAY_PAYMENT_METHOD_NAME . "\n";
}

// Check settings
$methodName = defined('NAGORIKPAY_PAYMENT_METHOD_NAME') ? NAGORIKPAY_PAYMENT_METHOD_NAME : 'nagorikpay';
echo "\nSettings:\n";
echo "- Status: " . get_payment_setting('status', $methodName, 'not set') . "\n";
echo "- Name: " . get_payment_setting('name', $methodName, 'not set') . "\n";
echo "- API Key: " . (get_payment_setting('api_key', $methodName) ? 'SET' : 'NOT SET') . "\n";
echo "- Mode: " . get_payment_setting('mode', $methodName, 'not set') . "\n";

// Check if service can be instantiated
try {
    $service = app(\Botble\NagorikPay\Services\NagorikPayService::class);
    echo "\nService: CAN BE INSTANTIATED\n";
} catch (Exception $e) {
    echo "\nService Error: " . $e->getMessage() . "\n";
}

// Check routes
echo "\nRoutes:\n";
$routes = [
    'payments.nagorikpay.success',
    'payments.nagorikpay.cancel',
    'payments.nagorikpay.webhook'
];

foreach ($routes as $routeName) {
    try {
        $url = route($routeName);
        echo "- $routeName: $url\n";
    } catch (Exception $e) {
        echo "- $routeName: ERROR - " . $e->getMessage() . "\n";
    }
}

// Check if payment plugin is active
echo "\nPayment plugin active: " . (is_plugin_active('payment') ? 'YES' : 'NO') . "\n";

echo "\n=== Debug Complete ===\n";
