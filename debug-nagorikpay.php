<?php

// Bootstrap Laravel
require __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== NagorikPay Debug ===\n\n";

// Test 1: Check if plugin is active
echo "1. Checking plugin status...\n";
try {
    $activePlugins = get_active_plugins();
    $isActive = in_array('nagorikpay', $activePlugins);
    echo "   Plugin Active: " . ($isActive ? '✅ YES' : '❌ NO') . "\n";
} catch (Exception $e) {
    echo "   ❌ Error checking plugin status: " . $e->getMessage() . "\n";
    exit;
}

// Test 2: Check constants
echo "\n2. Checking constants...\n";
$constantsDefined = defined('NAGORIKPAY_PAYMENT_METHOD_NAME');
echo "   Constants Defined: " . ($constantsDefined ? '✅ YES' : '❌ NO') . "\n";

// Test 3: Check if payment plugin is active
echo "\n3. Checking payment plugin...\n";
try {
    $paymentActive = is_plugin_active('payment');
    echo "   Payment Plugin: " . ($paymentActive ? '✅ ACTIVE' : '❌ INACTIVE') . "\n";
} catch (Exception $e) {
    echo "   ❌ Error checking payment plugin: " . $e->getMessage() . "\n";
}

// Test 4: Test form creation
echo "\n4. Testing form creation...\n";
try {
    $form = \Botble\NagorikPay\Forms\NagorikPaymentMethodForm::create();
    echo "   ✅ Form can be created\n";
} catch (Exception $e) {
    echo "   ❌ Form error: " . $e->getMessage() . "\n";
}

// Test 5: Check filter constants
echo "\n5. Checking filter constants...\n";
$filters = [
    'PAYMENT_FILTER_ADDITIONAL_PAYMENT_METHODS',
    'PAYMENT_METHODS_SETTINGS_PAGE',
    'BASE_FILTER_ENUM_ARRAY'
];

foreach ($filters as $filter) {
    $defined = defined($filter);
    echo "   $filter: " . ($defined ? '✅ DEFINED' : '❌ NOT DEFINED') . "\n";
}

echo "\n=== Debug Complete ===\n";
