<?php

namespace Bo<PERSON><PERSON>\Ecommerce\Http\Controllers;

use Bo<PERSON>ble\DataSynchronize\Http\Controllers\ImportController;
use Bo<PERSON>ble\DataSynchronize\Importer\Importer;
use Botble\Ecommerce\Importers\ProductInventoryImporter;

class ImportProductInventoryController extends ImportController
{
    protected function getImporter(): Importer
    {
        return ProductInventoryImporter::make();
    }
}
