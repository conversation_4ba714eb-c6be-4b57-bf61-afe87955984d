# NagorikPay Payment Gateway Plugin

A Laravel payment gateway plugin for NagorikPay integration, supporting popular payment methods in Bangladesh including bKash, Nagad, Rocket, and more.

## Features

- ✅ Secure payment processing through NagorikPay API
- ✅ Support for multiple payment methods (bKash, Nagad, Rocket, etc.)
- ✅ Real-time payment verification
- ✅ Webhook support for automatic payment status updates
- ✅ Sandbox and Live mode support
- ✅ Easy admin configuration
- ✅ Multi-language support
- ✅ Responsive payment interface

## Installation

1. **Upload Plugin**: Place the `nagorikpay` folder in `platform/plugins/` directory
2. **Activate Plugin**: Go to Admin Panel → Plugins → Activate NagorikPay
3. **Configure Settings**: Go to Admin Panel → Settings → Payment Methods → NagorikPay

## Configuration

### Required Settings

1. **API Key**: Your NagorikPay API key (get from NagorikPay dashboard)
2. **Mode**: Choose between Sandbox (testing) or Live (production)
3. **Payment Method Name**: Display name for customers
4. **Description**: Payment method description shown to customers

### API Endpoints Used

- **Payment Creation**: `https://secure-pay.nagorikpay.com/api/payment/create`
- **Payment Verification**: `https://secure-pay.nagorikpay.com/api/payment/verify`

## Usage

1. **Enable the Plugin**: Activate in admin panel
2. **Configure API Key**: Add your NagorikPay API key
3. **Set Mode**: Choose Sandbox for testing, Live for production
4. **Test Payment**: Make a test purchase to verify integration

## Webhook Configuration

The plugin automatically handles webhooks at:
```
https://yourdomain.com/payments/nagorikpay/webhook
```

Make sure to configure this URL in your NagorikPay dashboard.

## Payment Flow

1. Customer selects NagorikPay at checkout
2. Order is created with pending status
3. Customer is redirected to NagorikPay payment page
4. After payment, customer returns to success/cancel page
5. Webhook updates payment status automatically
6. Order status is updated based on payment result

## Support

For support and questions:
- Plugin Issues: Contact your developer
- NagorikPay API Issues: Contact NagorikPay support
- Documentation: https://nagorikpay.com

## Version

Current Version: 1.0.0
Minimum Core Version: 7.0.0

## License

This plugin is proprietary software. All rights reserved.
