# NagorikPay Payment Gateway Plugin

This plugin integrates NagorikPay payment gateway with Botble CMS, based on the WHMCS NagorikPay plugin structure.

## Features

- **Multiple Payment Methods**: Supports bKash, Nagad, Rocket, and card payments
- **Currency Support**: BDT and USD with automatic conversion
- **Secure Payments**: Uses NagorikPay's secure payment processing
- **Webhook Integration**: Automatic payment verification and status updates
- **Easy Configuration**: Simple admin panel configuration

## Installation

1. Copy the plugin to `platform/plugins/nagorikpay/`
2. Activate the plugin in admin panel
3. Configure API settings in Payment Methods

## Configuration

### Required Settings

- **API Key**: Your NagorikPay API key
- **Currency Rate**: USD to BDT conversion rate (default: 85)

### Getting API Key

1. Visit [https://nagorikpay.com](https://nagorikpay.com)
2. Register for an account
3. Get your API key from the dashboard
4. Enter the API key in plugin settings

## How It Works

### Payment Flow (Based on WHMCS Plugin)

1. **Payment Initiation**: Customer selects NagorikPay at checkout
2. **API Call**: Plugin calls NagorikPay API to create payment
3. **Redirect**: Customer is redirected to NagorikPay payment page
4. **Payment**: Customer completes payment using preferred method
5. **Webhook**: NagorikPay sends webhook to verify payment
6. **Verification**: Plugin verifies payment status with NagorikPay API
7. **Completion**: Order status is updated based on payment result

### Currency Handling

- **BDT**: Used directly without conversion
- **USD**: Converted to BDT using configured rate

### Webhook URLs

The plugin automatically generates these URLs:
- **Webhook**: `/payments/nagorikpay/webhook`
- **Success**: `/payments/nagorikpay/success`  
- **Cancel**: `/payments/nagorikpay/cancel`

## API Endpoints

### NagorikPay API

- **Create Payment**: `https://secure-pay.nagorikpay.com/api/payment/create`
- **Verify Payment**: `https://secure-pay.nagorikpay.com/api/payment/verify`

## Files Structure

```
platform/plugins/nagorikpay/
├── plugin.json                           # Plugin metadata
├── helpers/helpers.php                   # Constants and helper functions
├── src/
│   ├── Providers/
│   │   ├── NagorikPayServiceProvider.php # Main service provider
│   │   └── HookServiceProvider.php       # Payment hooks
│   ├── Services/
│   │   └── NagorikPayService.php         # Payment processing logic
│   └── Http/Controllers/
│       └── NagorikPayController.php      # Webhook and callback handling
├── routes/web.php                        # Plugin routes
├── resources/
│   ├── views/
│   │   ├── settings.blade.php            # Admin settings form
│   │   └── methods.blade.php             # Checkout payment method
│   └── lang/en/nagorikpay.php           # Language translations
└── public/images/                        # Payment method logos
```

## Differences from WHMCS Plugin

### WHMCS Structure
- Single `nagorikpay.php` file with functions
- `callback/nagorikpay.php` for webhooks
- Direct WHMCS API integration

### Botble CMS Structure  
- Object-oriented with service providers
- Separate controllers for webhooks
- Laravel/Botble framework integration
- Blade templates for views

### Key Adaptations

1. **Payment Creation**: WHMCS `nagorikpay_payment_url()` → `NagorikPayService::makePayment()`
2. **Configuration**: WHMCS `nagorikpay_config()` → Blade settings form
3. **Webhooks**: WHMCS callback file → Laravel controller
4. **Payment Verification**: Same API calls, different implementation

## Support

For support and documentation, visit [https://nagorikpay.com](https://nagorikpay.com)

## Version

- **Plugin Version**: 1.0.0
- **Based on**: WHMCS NagorikPay Plugin
- **Compatible with**: Botble CMS 7.0+
