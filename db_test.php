<?php
try {
    $pdo = new PDO('mysql:host=127.0.0.1;port=4409;dbname=naru', 'root', '');
    echo "Database connection successful!\n";
    
    // Test query
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "Found " . count($tables) . " tables in database.\n";
    
} catch(Exception $e) {
    echo "Connection failed: " . $e->getMessage() . "\n";
}
?>
