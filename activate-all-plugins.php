<?php

// Bootstrap Laravel
require __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Botble\PluginManagement\Services\PluginService;

// Get the plugin service
$pluginService = app(PluginService::class);

// List of plugins to activate
$plugins = [
    'ads',
    'analytics',
    'audit-log',
    'backup',
    'blog',
    'captcha',
    'contact',
    'cookie-consent',
    'ecommerce',
    'faq',
    'language',
    'language-advanced',
    'location',
    'marketplace',
    'mollie',
    'newsletter',
    'payment',
    'paypal',
    'paypal-payout',
    'paystack',
    'razorpay',
    'shippo',
    'simple-slider',
    'social-login',
    'sslcommerz',
    'stripe',
    'translation',
    'ecommerce-custom-fields'
];

// Activate each plugin
foreach ($plugins as $plugin) {
    echo "Activating $plugin... ";
    try {
        $result = $pluginService->activate($plugin);
        echo $result ? "Success" : "Failed";
    } catch (Exception $e) {
        echo "Error: " . $e->getMessage();
    }
    echo "\n";
}

echo "\nAll plugins activation attempted.\n";
