<?php

use Bo<PERSON><PERSON>\Base\Facades\EmailHandler;
use Botble\Base\Facades\MetaBox;
use Bo<PERSON>ble\Base\Forms\Fields\HtmlField;
use Botble\Base\Forms\Fields\MediaImageField;
use Botble\Ecommerce\Facades\EcommerceHelper;
use Bo<PERSON>ble\Ecommerce\Facades\FlashSale;
use Botble\Ecommerce\Supports\FlashSaleSupport;
use Botble\Marketplace\Facades\MarketplaceHelper;
use Botble\Marketplace\Forms\StoreForm;
use Botble\Marketplace\Forms\VendorStoreForm;
use Botble\Media\Facades\RvMedia;
use Botble\Menu\Facades\Menu;
use Botble\Newsletter\Facades\Newsletter;
use Botble\SocialLogin\Facades\SocialService;
use Botble\Theme\Facades\Theme;
use Botble\Theme\Supports\ThemeSupport;
use Botble\Theme\Typography\TypographyItem;
use Illuminate\Routing\Events\RouteMatched;
use Theme\Farmart\Supports\Wishlist;

register_page_template([
    'default' => __('Default'),
    'homepage' => __('Homepage'),
    'full-width' => __('Full Width'),
    'coming-soon' => __('Coming Soon'),
]);

RvMedia::addSize('small', 300, 300);

Menu::addMenuLocation('header-navigation', __('Header Navigation'));

function available_socials_store(): array
{
    return [
        'facebook' => 'Facebook',
        'twitter' => 'Twitter',
        'instagram' => 'Instagram',
        'youtube' => 'YouTube',
        'linkedin' => 'Linkedin',
    ];
}

app()->booted(function () {
    ThemeSupport::registerSocialLinks();
    ThemeSupport::registerSocialSharing();
    ThemeSupport::registerSiteLogoHeight(45);
    ThemeSupport::registerSiteCopyright();

    // Add immediate slider fix CSS - LOAD FIRST
    add_action(THEME_FRONT_HEADER, function () {
        echo '<link rel="stylesheet" href="' . asset('themes/farmart/css/immediate-slider-fix.css') . '">';
        echo '<script src="' . asset('themes/farmart/js/immediate-slider-fix.js') . '"></script>';
    }, 10);

    // Add custom CSS to set site width to 1350px
    add_action(THEME_FRONT_HEADER, function () {
        echo '<link rel="stylesheet" href="' . asset('themes/farmart/css/custom-width.css') . '">';
    }, 15);

    // Add custom CSS to hide banner on search products page
    add_action(THEME_FRONT_HEADER, function () {
        echo '<link rel="stylesheet" href="' . asset('themes/farmart/css/hide-search-banner.css') . '">';
    }, 16);

    // Add JavaScript to remove banner on search products page
    add_action(THEME_FRONT_FOOTER, function () {
        echo '<script src="' . asset('themes/farmart/js/remove-search-banner.js') . '"></script>';
    }, 16);

    // Add custom CSS for slider modifications
    add_action(THEME_FRONT_HEADER, function () {
        echo '<link rel="stylesheet" href="' . asset('themes/farmart/css/custom-slider.css') . '">';
    }, 17);

    // Add JavaScript to remove slider background
    add_action(THEME_FRONT_FOOTER, function () {
        echo '<script src="' . asset('themes/farmart/js/remove-slider-bg.js') . '"></script>';
    }, 18);

    // Add custom CSS for product hover buttons positioning
    add_action(THEME_FRONT_HEADER, function () {
        echo '<link rel="stylesheet" href="' . asset('themes/farmart/css/custom-hover-buttons.css') . '">';
    }, 19);

    // Add custom CSS to fix modal close button
    add_action(THEME_FRONT_HEADER, function () {
        echo '<link rel="stylesheet" href="' . asset('themes/farmart/css/fix-modal-close.css') . '">';
    }, 20);

    // Add custom CSS to hide breadcrumbs on mobile and tablet views
    add_action(THEME_FRONT_HEADER, function () {
        echo '<link rel="stylesheet" href="' . asset('themes/farmart/css/hide-mobile-breadcrumbs.css') . '">';

        // Add inline style for immediate effect
        echo '<style>
            @media (max-width: 991px) {
                .page-breadcrumbs,
                nav[aria-label="breadcrumb"],
                .breadcrumb,
                .mobile-breadcrumb-hide {
                    display: none !important;
                }
            }
        </style>';
    }, 21);

    // Add custom CSS to hide footer on mobile and tablet views
    add_action(THEME_FRONT_HEADER, function () {
        echo '<link rel="stylesheet" href="' . asset('themes/farmart/css/hide-mobile-footer.css') . '">';

        // Add inline style for immediate effect
        echo '<style>
            @media (max-width: 991px) {
                #footer {
                    display: none !important;
                }

                /* Make sure the mobile footer navigation remains visible */
                .footer-mobile {
                    display: block !important;
                }
            }
        </style>';
    }, 22);

    // Add custom CSS for mobile price tag
    add_action(THEME_FRONT_HEADER, function () {
        echo '<link rel="stylesheet" href="' . asset('themes/farmart/css/mobile-price-tag.css') . '">';

        // Add CSS to remove action buttons from product cards
        echo '<link rel="stylesheet" href="' . asset('themes/farmart/css/remove-action-buttons.css') . '">';

        // Add CSS for list view fixes
        echo '<link rel="stylesheet" href="' . asset('themes/farmart/css/list-view-fixes.css') . '">';

        // Add inline style for immediate effect
        echo '<style>
            @media (max-width: 991px) {
                .mobile-price-tag {
                    background-color: #ff6633 !important;
                    margin: -10px -15px 15px -15px !important;
                    padding: 10px 15px !important;
                    width: calc(100% + 30px) !important;
                    position: relative !important;
                    color: #fff !important;
                    display: block !important;
                }

                .mobile-price-tag .price-tag-content {
                    display: flex !important;
                    align-items: center !important;
                    justify-content: flex-start !important;
                    height: 30px !important;
                }

                .mobile-price-tag .sale-price {
                    font-size: 18px !important;
                    font-weight: 700 !important;
                    margin-right: 10px !important;
                    color: #fff !important;
                }

                .mobile-price-tag .regular-price {
                    font-size: 14px !important;
                    text-decoration: line-through !important;
                    opacity: 0.8 !important;
                    color: #fff !important;
                }

                .mobile-price-tag .regular-price-only {
                    font-size: 18px !important;
                    font-weight: 700 !important;
                    color: #fff !important;
                }

                .product-content-box {
                    padding-top: 10px !important;
                }

                /* Hide desktop price on mobile */
                .product-details .d-none.d-md-block {
                    display: none !important;
                }

                /* Remove all action buttons from product cards */
                .product-loop__buttons,
                .quick-view-button,
                .wishlist-button,
                .compare-button,
                .product-loop_button,
                .product-loop_action,
                .product-inner .product-loop__buttons,
                .product-inner .quick-view-button,
                .product-inner .wishlist-button,
                .product-inner .compare-button,
                .product-inner .product-loop_button,
                .product-inner .product-loop_action {
                    display: none !important;
                    visibility: hidden !important;
                    opacity: 0 !important;
                    pointer-events: none !important;
                    width: 0 !important;
                    height: 0 !important;
                    position: absolute !important;
                    z-index: -9999 !important;
                    clip: rect(0, 0, 0, 0) !important;
                }
            }
        </style>';

        // Add direct script to ensure price tag is visible
        echo '<script>
            document.addEventListener("DOMContentLoaded", function() {
                if (window.innerWidth <= 991) {
                    // Force display of price tags
                    var priceTags = document.querySelectorAll(".mobile-price-tag");
                    priceTags.forEach(function(tag) {
                        tag.style.display = "block";
                        tag.style.backgroundColor = "#ff6633";
                        tag.style.color = "#fff";
                        tag.style.margin = "-10px -15px 15px -15px";
                        tag.style.padding = "10px 15px";
                        tag.style.width = "calc(100% + 30px)";
                        tag.style.position = "relative";
                    });

                    // Force content box padding
                    var contentBoxes = document.querySelectorAll(".product-content-box");
                    contentBoxes.forEach(function(box) {
                        box.style.paddingTop = "10px";
                    });
                }
            });
        </script>';
    }, 23);


    // Add direct inline script to immediately hide the Wishlist and Compare buttons
    add_action(THEME_FRONT_HEADER, function () {
        echo '<style>
            /* Target the specific heart icon and compare icon at the top */
            .heart-icon, .compare-icon,
            a[href*="wishlist"], a[href*="compare"],
            /* Target the specific elements in the screenshot */
            .header-top a[href*="wishlist"], .header-top a[href*="compare"],
            /* Target by direct text content */
            a:has(span:contains("Wishlist")), a:has(span:contains("Compare")),
            /* Target the specific icons */
            svg use[href="#svg-icon-wishlist"], svg use[href="#svg-icon-compare"],
            /* Target the parent elements */
            a:has(svg use[href="#svg-icon-wishlist"]), a:has(svg use[href="#svg-icon-compare"]),
            /* Target the specific elements in the screenshot */
            .header-info a[href*="wishlist"], .header-info a[href*="compare"],
            /* Target the specific elements by their appearance in the screenshot */
            .header-info a:nth-child(2), .header-info a:nth-child(3),
            /* Target the specific elements by their position */
            .header-info > ul > li:has(a[href*="wishlist"]), .header-info > ul > li:has(a[href*="compare"]),
            /* Target the specific elements by their text content */
            .header-info a:contains("Wishlist"), .header-info a:contains("Compare"),
            /* Target the specific heart and compare icons at the top */
            .icon-heart, .icon-repeat {
                display: none !important;
            }
        </style>';

        echo '<script>
            // Run immediately without waiting for DOMContentLoaded
            (function() {
                // Function to hide elements
                function hideElements() {
                    // Hide by direct selectors
                    var selectors = [
                        ".heart-icon", ".compare-icon",
                        "a[href*=\'wishlist\']", "a[href*=\'compare\']",
                        ".header-top a[href*=\'wishlist\']", ".header-top a[href*=\'compare\']",
                        ".header-info a[href*=\'wishlist\']", ".header-info a[href*=\'compare\']",
                        ".icon-heart", ".icon-repeat"
                    ];

                    selectors.forEach(function(selector) {
                        var elements = document.querySelectorAll(selector);
                        elements.forEach(function(el) {
                            el.style.display = "none";
                        });
                    });

                    // Hide by text content
                    var allLinks = document.querySelectorAll("a");
                    allLinks.forEach(function(link) {
                        var text = link.textContent.trim();
                        if (text === "Wishlist" || text === "Compare") {
                            link.style.display = "none";
                            // Also hide parent elements
                            var parent = link.parentElement;
                            while (parent && parent.tagName !== "BODY") {
                                if (parent.tagName === "LI") {
                                    parent.style.display = "none";
                                }
                                parent = parent.parentElement;
                            }
                        }
                    });
                }

                // Run immediately
                hideElements();

                // Also run when DOM is loaded
                document.addEventListener("DOMContentLoaded", hideElements);

                // Run periodically to catch any dynamically added elements
                setInterval(hideElements, 100);
            })();
        </script>';
    }, 1);

    // Add JavaScript to move product hover buttons to the right
    add_action(THEME_FRONT_FOOTER, function () {
        echo '<script src="' . asset('themes/farmart/js/custom-hover-buttons.js') . '"></script>';
    }, 20);

    // Add JavaScript to remove Wishlist and Compare buttons
    add_action(THEME_FRONT_FOOTER, function () {
        echo '<script src="' . asset('themes/farmart/js/remove-wishlist-compare.js') . '"></script>';
    }, 19);

    // Add JavaScript to remove all action buttons from product cards
    add_action(THEME_FRONT_FOOTER, function () {
        echo '<script src="' . asset('themes/farmart/js/remove-action-buttons.js') . '"></script>';
    }, 18);

    // Add direct inline script to move buttons
    add_action(THEME_FRONT_FOOTER, function () {
        echo '<script>
            document.addEventListener("DOMContentLoaded", function() {
                // Apply styles directly to elements
                document.querySelectorAll(".product-loop__buttons").forEach(function(el) {
                    el.style.right = "-11px";
                    el.style.position = "absolute";
                    el.style.top = "0";
                    el.style.zIndex = "10";
                });

                // Add hover effect
                document.querySelectorAll(".product-inner").forEach(function(product) {
                    product.style.overflow = "visible";

                    product.addEventListener("mouseenter", function() {
                        const buttons = this.querySelectorAll(".product-loop__buttons .product-loop_action");
                        buttons.forEach(function(btn) {
                            btn.style.transform = "translateX(0)";
                        });
                    });

                    product.addEventListener("mouseleave", function() {
                        const buttons = this.querySelectorAll(".product-loop__buttons .product-loop_action");
                        buttons.forEach(function(btn) {
                            btn.style.transform = "translateX(10px)";
                        });
                    });
                });
            });
        </script>';
    }, 22);

    // Add new JavaScript to fix modal close button with custom implementation
    add_action(THEME_FRONT_FOOTER, function () {
        echo '<script src="' . asset('themes/farmart/js/modal-close-fix.js') . '"></script>';
    }, 23);

    // Add script to ensure mobile price tag is visible
    add_action(THEME_FRONT_FOOTER, function () {
        echo '<script>
            // Force mobile price tag to be visible
            (function() {
                function forceMobilePriceTag() {
                    if (window.innerWidth <= 991) {
                        // Force display of price tags
                        var priceTags = document.querySelectorAll(".mobile-price-tag");
                        priceTags.forEach(function(tag) {
                            tag.style.display = "block";
                            tag.style.backgroundColor = "#ff6633";
                            tag.style.color = "#fff";
                            tag.style.margin = "-10px -15px 15px -15px";
                            tag.style.padding = "10px 15px";
                            tag.style.width = "calc(100% + 30px)";
                            tag.style.position = "relative";

                            // Force all child elements to be visible
                            var children = tag.querySelectorAll("*");
                            children.forEach(function(child) {
                                if (child.classList.contains("price-tag-content")) {
                                    child.style.display = "flex";
                                    child.style.alignItems = "center";
                                    child.style.height = "40px"; // Updated to 40px for consistency
                                }
                                if (child.classList.contains("sale-price") || child.classList.contains("regular-price-only")) {
                                    child.style.fontSize = "18px";
                                    child.style.fontWeight = "700";
                                    child.style.color = "#fff";
                                }
                                if (child.classList.contains("regular-price")) {
                                    child.style.fontSize = "14px";
                                    child.style.textDecoration = "line-through";
                                    child.style.opacity = "0.8";
                                    child.style.color = "#fff";
                                }
                            });
                        });

                        // Force content box padding
                        var contentBoxes = document.querySelectorAll(".product-content-box");
                        contentBoxes.forEach(function(box) {
                            box.style.paddingTop = "10px";
                        });

                        // Hide desktop price display
                        var desktopPrices = document.querySelectorAll(".product-details .d-none.d-md-block");
                        desktopPrices.forEach(function(price) {
                            price.style.display = "none";
                        });

                        // Fix for related products and more to love sections
                        var priceTagContainers = document.querySelectorAll(".related-products-grid .product-inner .product-details > div, .more-to-love-products-grid .product-inner .product-details > div");
                        priceTagContainers.forEach(function(container) {
                            // Ensure all price tag containers have the same height
                            container.style.height = "40px";
                            container.style.minHeight = "40px";

                            // Fix for sale price containers (with both sale and regular price)
                            var saleContainer = container.querySelector("div[style*=\"flex-direction: column\"]");
                            if (saleContainer) {
                                saleContainer.style.height = "40px";
                                saleContainer.parentElement.style.height = "40px";
                            }
                        });
                    }
                }

                // Run immediately
                forceMobilePriceTag();

                // Run when DOM is loaded
                document.addEventListener("DOMContentLoaded", forceMobilePriceTag);

                // Run when window is resized
                window.addEventListener("resize", forceMobilePriceTag);

                // Run periodically to catch any dynamic changes
                setInterval(forceMobilePriceTag, 500);
            })();
        </script>';
    }, 25);

    // Add price tag height fix script
    add_action(THEME_FRONT_FOOTER, function () {
        echo '<script src="' . asset('themes/farmart/js/price-tag-height-fix.js') . '"></script>';
    }, 26);

    // Add mobile sticky bar fix
    add_action(THEME_FRONT_HEADER, function () {
        echo '<link rel="stylesheet" href="' . asset('themes/farmart/css/mobile-sticky-bar-fix.css') . '">';
    }, 27);

    // Add custom CSS to fix slider initialization issues
    add_action(THEME_FRONT_HEADER, function () {
        echo '<link rel="stylesheet" href="' . asset('themes/farmart/css/slider-fix.css') . '">';

        // Add immediate JavaScript in head to fix layout ASAP
        echo '<script>
            (function() {
                function hideExtraSlides() {
                    var sliders = document.querySelectorAll(".slick-slides-carousel:not(.slick-initialized)");
                    sliders.forEach(function(slider) {
                        var slides = slider.children;
                        for (var i = 1; i < slides.length; i++) {
                            slides[i].style.display = "none";
                        }
                    });

                    var galleries = document.querySelectorAll(".bb-product-gallery-images:not(.slick-initialized)");
                    galleries.forEach(function(gallery) {
                        var items = gallery.children;
                        for (var i = 1; i < items.length; i++) {
                            items[i].style.display = "none";
                        }
                    });
                }

                // Run as soon as possible
                if (document.readyState === "loading") {
                    document.addEventListener("DOMContentLoaded", hideExtraSlides);
                } else {
                    hideExtraSlides();
                }

                // Also run on window load
                window.addEventListener("load", hideExtraSlides);

                // Run periodically for the first few seconds
                var attempts = 0;
                var interval = setInterval(function() {
                    hideExtraSlides();
                    attempts++;
                    if (attempts > 20) { // Stop after 20 attempts (1 second)
                        clearInterval(interval);
                    }
                }, 50);
            })();
        </script>';

        // Add immediate inline CSS for critical fixes
        echo '<style>
            /* CRITICAL FIX: Prevent vertical image stacking during slider initialization */
            .slick-slides-carousel:not(.slick-initialized) {
                height: auto !important;
                overflow: hidden !important;
                position: relative !important;
            }

            .slick-slides-carousel:not(.slick-initialized) > * {
                position: absolute !important;
                top: 0 !important;
                left: 0 !important;
                width: 100% !important;
                opacity: 0 !important;
                transition: none !important;
            }

            .slick-slides-carousel:not(.slick-initialized) > *:first-child {
                position: relative !important;
                opacity: 1 !important;
            }

            /* Product gallery fixes */
            .bb-product-gallery-images:not(.slick-initialized) {
                overflow: hidden !important;
                position: relative !important;
            }

            .bb-product-gallery-images:not(.slick-initialized) > * {
                position: absolute !important;
                top: 0 !important;
                left: 0 !important;
                width: 100% !important;
                opacity: 0 !important;
            }

            .bb-product-gallery-images:not(.slick-initialized) > *:first-child {
                position: relative !important;
                opacity: 1 !important;
            }

            /* Ensure proper dimensions */
            .section-content__slider .slick-slides-carousel:not(.slick-initialized) {
                height: 384px !important;
                max-height: 384px !important;
            }

            @media (max-width: 767px) {
                .section-content__slider .slick-slides-carousel:not(.slick-initialized) {
                    height: 45vw !important;
                    max-height: none !important;
                }
            }
        </style>';
    }, 28);

    // Add immediate JavaScript fix for sliders
    add_action(THEME_FRONT_FOOTER, function () {
        echo '<script>
            // Immediate fix for slider initialization
            (function() {
                function fixSliderLayout() {
                    // Fix main sliders
                    document.querySelectorAll(".slick-slides-carousel:not(.slick-initialized)").forEach(function(slider) {
                        var slides = slider.children;
                        for (var i = 1; i < slides.length; i++) {
                            slides[i].style.display = "none";
                        }
                    });

                    // Fix product galleries
                    document.querySelectorAll(".bb-product-gallery-images:not(.slick-initialized)").forEach(function(gallery) {
                        var items = gallery.children;
                        for (var i = 1; i < items.length; i++) {
                            items[i].style.display = "none";
                        }
                    });
                }

                // Run immediately
                fixSliderLayout();

                // Run when DOM is ready
                if (document.readyState === "loading") {
                    document.addEventListener("DOMContentLoaded", fixSliderLayout);
                } else {
                    fixSliderLayout();
                }

                // Run periodically until sliders are initialized
                var checkInterval = setInterval(function() {
                    fixSliderLayout();

                    // Stop checking if all sliders are initialized
                    if (document.querySelectorAll(".slick-slides-carousel:not(.slick-initialized)").length === 0 &&
                        document.querySelectorAll(".bb-product-gallery-images:not(.slick-initialized)").length === 0) {
                        clearInterval(checkInterval);
                    }
                }, 50);

                // Stop checking after 5 seconds max
                setTimeout(function() {
                    clearInterval(checkInterval);
                }, 5000);
            })();
        </script>';
    }, 29);

    add_action(THEME_FRONT_FOOTER, function () {
        echo '<script src="' . asset('themes/farmart/js/mobile-sticky-bar-fix.js') . '"></script>';

        // Add inline script for immediate effect on mobile and tablet only
        echo '<script>
            // Immediately fix sticky bar on mobile and tablet
            (function() {
                if (window.innerWidth < 992) {
                    // Find the sticky bar elements
                    var stickyBar = document.querySelector("#sticky-add-to-cart .sticky-atc-wrap");
                    var mobileStickyBar = document.querySelector(".mobile-sticky-add-to-cart");

                    // Force display for sticky bar
                    if (stickyBar) {
                        stickyBar.style.display = "block";
                        stickyBar.style.visibility = "visible";
                        stickyBar.style.opacity = "1";
                        stickyBar.style.position = "fixed";
                        stickyBar.style.bottom = "60px";
                        stickyBar.style.zIndex = "998";
                        stickyBar.classList.add("sticky-atc-shown");
                    }

                    // Force display for mobile sticky bar
                    if (mobileStickyBar) {
                        mobileStickyBar.style.display = "block";
                        mobileStickyBar.style.visibility = "visible";
                        mobileStickyBar.style.opacity = "1";
                    }
                }
            })();
        </script>';
    }, 27);

    // Add direct inline script for immediate effect
    add_action(THEME_FRONT_FOOTER, function () {
        echo '<script>
            // Immediately add event listener for quick view buttons
            document.addEventListener("click", function(e) {
                if (e.target.closest(".quick-view")) {
                    // Wait for modal to open and enhance the existing close button
                    setTimeout(function() {
                        var modal = document.getElementById("product-quick-view-modal");
                        if (!modal) return;

                        var closeButton = modal.querySelector(".btn-close");
                        if (!closeButton) return;

                        // Enhance the existing close button
                        closeButton.style.zIndex = "999999";
                        closeButton.style.position = "absolute";
                        closeButton.style.top = "20px";
                        closeButton.style.right = "20px";
                        closeButton.style.opacity = "1";
                        closeButton.style.backgroundColor = "white";
                        closeButton.style.border = "1px solid #ccc";
                        closeButton.style.borderRadius = "50%";
                        closeButton.style.width = "40px";
                        closeButton.style.height = "40px";
                        closeButton.style.display = "flex";
                        closeButton.style.alignItems = "center";
                        closeButton.style.justifyContent = "center";
                        closeButton.style.cursor = "pointer";

                        // Add enhanced click event
                        closeButton.addEventListener("click", function(e) {
                            e.preventDefault();
                            e.stopPropagation();

                            // Close modal using multiple methods
                            var modal = document.getElementById("product-quick-view-modal");
                            if (modal) {
                                // Method 1: Bootstrap
                                if (typeof bootstrap !== "undefined") {
                                    var bsModal = bootstrap.Modal.getInstance(modal);
                                    if (bsModal) bsModal.hide();
                                }

                                // Method 2: jQuery
                                if (typeof jQuery !== "undefined") {
                                    jQuery(modal).modal("hide");
                                }

                                // Method 3: Direct DOM
                                modal.classList.remove("show");
                                modal.style.display = "none";
                                document.body.classList.remove("modal-open");

                                // Remove backdrop
                                var backdrops = document.querySelectorAll(".modal-backdrop");
                                backdrops.forEach(function(backdrop) {
                                    backdrop.remove();
                                });
                            }
                        });
                    }, 500);
                }
            });

            // Add keyboard shortcut (ESC key) to close the modal
            document.addEventListener("keydown", function(e) {
                if (e.key === "Escape") {
                    var modal = document.getElementById("product-quick-view-modal");
                    if (modal && (modal.classList.contains("show") || getComputedStyle(modal).display !== "none")) {
                        // Method 1: Bootstrap
                        if (typeof bootstrap !== "undefined") {
                            var bsModal = bootstrap.Modal.getInstance(modal);
                            if (bsModal) bsModal.hide();
                        }

                        // Method 2: jQuery
                        if (typeof jQuery !== "undefined") {
                            jQuery(modal).modal("hide");
                        }

                        // Method 3: Direct DOM
                        modal.classList.remove("show");
                        modal.style.display = "none";
                        document.body.classList.remove("modal-open");

                        // Remove backdrop
                        var backdrops = document.querySelectorAll(".modal-backdrop");
                        backdrops.forEach(function(backdrop) {
                            backdrop.remove();
                        });
                    }
                }
            });
        </script>';
    }, 24);

    // Add body class for search page
    add_filter('theme_front_body_class', function ($classes) {
        if (request()->is('search') || request()->has('q')) {
            $classes[] = 'search-page';
        }
        return $classes;
    });

    if (is_plugin_active('newsletter')) {
        Newsletter::registerNewsletterPopup();
    }

    if (is_plugin_active('ecommerce')) {
        EcommerceHelper::registerProductVideo();
        EcommerceHelper::registerThemeAssets();
    }

    Theme::typography()
        ->registerFontFamily(new TypographyItem('primary', __('Primary'), 'Mulish'));

    register_sidebar([
        'id' => 'pre_footer_sidebar',
        'name' => __('Top footer sidebar'),
        'description' => __('Widgets in the blog page'),
    ]);

    register_sidebar([
        'id' => 'footer_sidebar',
        'name' => __('Footer sidebar'),
        'description' => __('Widgets in footer sidebar'),
    ]);

    register_sidebar([
        'id' => 'bottom_footer_sidebar',
        'name' => __('Bottom footer sidebar'),
        'description' => __('Widgets in bottom footer sidebar'),
    ]);

    if (is_plugin_active('ecommerce')) {
        register_sidebar([
            'id' => 'products_list_sidebar',
            'name' => __('Products list sidebar'),
            'description' => __('Widgets on header products list page'),
        ]);

        register_sidebar([
            'id' => 'product_detail_sidebar',
            'name' => __('Product detail sidebar'),
            'description' => __('Widgets in the product detail page'),
        ]);

        add_filter('ecommerce_quick_view_data', function (array $data): array {
            return [
                ...$data,
                'wishlistIds' => Wishlist::getWishlistIds([$data['product']->getKey()]),
            ];
        });
    }

    if (method_exists(FlashSaleSupport::class, 'addShowSaleCountLeftSetting')) {
        FlashSale::addShowSaleCountLeftSetting();
    }

    if (is_plugin_active('marketplace')) {
        StoreForm::extend(function (StoreForm $form) {
            $form->addAfter('cover_image', 'background', MediaImageField::class, [
                'label' => __('Background'),
                'metadata' => true,
                'colspan' => 2,
            ]);
        });

        VendorStoreForm::extend(function (VendorStoreForm $form) {
            $form
                ->addAfter('cover_image', 'background', MediaImageField::class, [
                    'label' => __('Background'),
                    'metadata' => true,
                    'colspan' => 2,
                ])
                ->when(! MarketplaceHelper::hideStoreSocialLinks(), function (VendorStoreForm $form) {
                    $store = $form->getModel();

                    $background = $store->getMetaData('background', true);
                    $socials = $store->getMetaData('socials', true);
                    $availableSocials = available_socials_store();

                    $view = Theme::getThemeNamespace() . '::views.marketplace.includes.extended-info-content';

                    $form->addBefore('submit', 'extended_info_content', HtmlField::class, [
                        'html' => view($view, compact('background', 'socials', 'availableSocials'))->render(),
                    ]);
                });
        });

        VendorStoreForm::afterSaving(function (VendorStoreForm $form) {
            $request = $form->getRequest();

            $store = $form->getModel();

            if ($request->hasFile('background_input')) {
                $result = RvMedia::handleUpload($request->file('background_input'), 0, 'stores');
                if (! $result['error']) {
                    MetaBox::saveMetaBoxData($store, 'background', $result['data']->url);
                }
            } elseif ($request->input('background')) {
                MetaBox::saveMetaBoxData($store, 'background', $request->input('background'));
            } elseif ($request->has('background')) {
                MetaBox::deleteMetaData($store, 'background');
            }

            if (! MarketplaceHelper::hideStoreSocialLinks() && $request->has('socials')) {
                $availableSocials = available_socials_store();
                $socials = collect((array) $request->input('socials', []))->filter(
                    function ($value, $key) use ($availableSocials) {
                        return filter_var($value, FILTER_VALIDATE_URL) && in_array($key, array_keys($availableSocials));
                    }
                );

                MetaBox::saveMetaBoxData($store, 'socials', $socials);
            }
        }, 230);
    }

    app('events')->listen(RouteMatched::class, function () {
        EmailHandler::addTemplateSettings(Theme::getThemeName(), [
            'name' => __('Theme emails'),
            'description' => __('Config email templates for theme'),
            'templates' => [
                'contact-seller' => [
                    'title' => __('Contact Seller'),
                    'description' => __(
                        'Email will be sent to the seller when someone contact from store profile page'
                    ),
                    'subject' => __('Message sent via your market profile on {{ site_title }}'),
                    'can_off' => true,
                ],
            ],
            'variables' => [
                'contact_message' => __('Contact seller message'),
                'customer_name' => __('Customer Name'),
                'customer_email' => __('Customer Email'),
                'store_name' => 'plugins/marketplace::marketplace.store_name',
                'store_phone' => 'plugins/marketplace::marketplace.store_phone',
                'store_address' => 'plugins/marketplace::marketplace.store_address',
                'store_url' => 'plugins/marketplace::marketplace.store_url',
                'store' => 'Store',
            ],
        ], 'themes');
    });

    if (is_plugin_active('social-login')) {
        $data = SocialService::getModule('customer');
        if ($data) {
            $data['view'] = Theme::getThemeNamespace('partials.social-login-options');
            $data['use_css'] = false;
            SocialService::registerModule($data);
        }
    }
});

if (! function_exists('theme_get_autoplay_speed_options')) {
    function theme_get_autoplay_speed_options(): array
    {
        $options = [2000, 3000, 4000, 5000, 6000, 7000, 8000, 9000, 10000];

        return array_combine($options, $options);
    }
}

if (! function_exists('get_store_list_layouts')) {
    function get_store_list_layouts(): array
    {
        return [
            'grid' => __('Grid'),
            'list' => __('List'),
        ];
    }
}
