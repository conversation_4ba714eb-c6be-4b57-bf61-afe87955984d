<?php

namespace Bo<PERSON>ble\Ecommerce\Repositories\Eloquent;

use Bo<PERSON>ble\Ecommerce\Models\Order;
use Bo<PERSON>ble\Ecommerce\Repositories\Interfaces\OrderInterface;
use Bo<PERSON>ble\Support\Repositories\Eloquent\RepositoriesAbstract;
use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Collection;

class OrderRepository extends RepositoriesAbstract implements OrderInterface
{
    public function getRevenueData(CarbonInterface $startDate, CarbonInterface $endDate, $select = []): Collection
    {
        return Order::getRevenueData($startDate, $endDate);
    }

    public function countRevenueByDateRange(CarbonInterface $startDate, CarbonInterface $endDate): float
    {
        return Order::countRevenueByDateRange($startDate, $endDate);
    }
}
