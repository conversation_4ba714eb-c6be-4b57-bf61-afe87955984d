[2025-07-11 07:13:34] local.ERROR: Class "Botble\NagorikPay\Forms\NagorikPaymentMethodForm" not found {"exception":"[object] (Error(code: 0): Class \"Botble\\NagorikPay\\Forms\\NagorikPaymentMethodForm\" not found at C:\\Users\\<USER>\\Desktop\\uu\\debug-nagorikpay.php:39)
[stacktrace]
#0 {main}
"} 
[2025-07-11 08:28:58] local.ERROR: There are no commands defined in the "plugin" namespace.

Did you mean one of these?
    cms:plugin
    cms:plugin:activate
    cms:plugin:assets
    cms:plugin:deactivate
    cms:plugin:remove {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"plugin\" namespace.

Did you mean one of these?
    cms:plugin
    cms:plugin:activate
    cms:plugin:assets
    cms:plugin:deactivate
    cms:plugin:remove at C:\\Users\\<USER>\\Desktop\\uu\\vendor\\symfony\\console\\Application.php:669)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\symfony\\console\\Application.php(720): Symfony\\Component\\Console\\Application->findNamespace('plugin')
#1 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\symfony\\console\\Application.php(266): Symfony\\Component\\Console\\Application->find('plugin:activate')
#2 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\Desktop\\uu\\artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 {main}
"} 
[2025-07-11 08:33:00] local.ERROR: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select `migration` from `migrations` order by `batch` asc, `migration` asc) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select `migration` from `migrations` order by `batch` asc, `migration` asc) at C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback('select `migrati...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select `migrati...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(785): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select `migrati...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `migrati...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select `migrati...', Array, false)
#5 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3100): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::pluck():3098}()
#7 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3096): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(53): Illuminate\\Database\\Query\\Builder->pluck('migration')
#9 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(118): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->getRan()
#10 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php(394): Illuminate\\Database\\Migrations\\Migrator->run('C:\\\\Users\\\\<USER>\\\\...')
#11 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php(103): Botble\\PluginManagement\\Services\\PluginService->runMigrations('payment')
#12 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\plugin-management\\src\\Commands\\PluginActivateCommand.php(28): Botble\\PluginManagement\\Services\\PluginService->activate('payment')
#13 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Botble\\PluginManagement\\Commands\\PluginActivateCommand->handle(Object(Botble\\PluginManagement\\Services\\PluginService))
#14 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#15 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#17 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#18 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#19 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#20 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#21 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Botble\\PluginManagement\\Commands\\PluginActivateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 C:\\Users\\<USER>\\Desktop\\uu\\artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it at C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(83): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', '', Array)
#2 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=127....', 'root', '', Array)
#3 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#4 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():179}()
#6 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#7 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getPdo()
#8 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(false)
#9 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():414}('select `migrati...', Array)
#10 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback('select `migrati...', Array, Object(Closure))
#11 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select `migrati...', Array, Object(Closure))
#12 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(785): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select `migrati...', Array, Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `migrati...', Array, Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select `migrati...', Array, false)
#15 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3100): Illuminate\\Database\\Query\\Builder->runSelect()
#16 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::pluck():3098}()
#17 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3096): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#18 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(53): Illuminate\\Database\\Query\\Builder->pluck('migration')
#19 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(118): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->getRan()
#20 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php(394): Illuminate\\Database\\Migrations\\Migrator->run('C:\\\\Users\\\\<USER>\\\\...')
#21 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\plugin-management\\src\\Services\\PluginService.php(103): Botble\\PluginManagement\\Services\\PluginService->runMigrations('payment')
#22 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\plugin-management\\src\\Commands\\PluginActivateCommand.php(28): Botble\\PluginManagement\\Services\\PluginService->activate('payment')
#23 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Botble\\PluginManagement\\Commands\\PluginActivateCommand->handle(Object(Botble\\PluginManagement\\Services\\PluginService))
#24 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#25 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#28 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#29 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#31 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Botble\\PluginManagement\\Commands\\PluginActivateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 C:\\Users\\<USER>\\Desktop\\uu\\artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 {main}
"} 
[2025-07-11 08:49:05] local.ERROR: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select `key` from `settings`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select `key` from `settings`) at C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback('select `key` fr...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select `key` fr...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(785): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select `key` fr...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `key` fr...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select `key` fr...', Array, true)
#5 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3100): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::pluck():3098}()
#7 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3096): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(889): Illuminate\\Database\\Query\\Builder->pluck('key', NULL)
#9 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php(45): Illuminate\\Database\\Eloquent\\Builder->pluck('key')
#10 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php(102): Botble\\Setting\\Supports\\DatabaseSettingStore->write(Array)
#11 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Botble\\Setting\\Supports\\SettingStore->save()
#12 C:\\Users\\<USER>\\Desktop\\uu\\fix-nagorikpay-settings.php(29): Illuminate\\Support\\Facades\\Facade::__callStatic('save', Array)
#13 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it at C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(83): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', '', Array)
#2 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=127....', 'root', '', Array)
#3 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#4 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():179}()
#6 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#7 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#8 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#9 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(true)
#10 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():414}('select `key` fr...', Array)
#11 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback('select `key` fr...', Array, Object(Closure))
#12 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select `key` fr...', Array, Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(785): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select `key` fr...', Array, Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `key` fr...', Array, Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select `key` fr...', Array, true)
#16 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3100): Illuminate\\Database\\Query\\Builder->runSelect()
#17 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::pluck():3098}()
#18 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3096): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(889): Illuminate\\Database\\Query\\Builder->pluck('key', NULL)
#20 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\platform\\setting\\src\\Supports\\DatabaseSettingStore.php(45): Illuminate\\Database\\Eloquent\\Builder->pluck('key')
#21 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\platform\\setting\\src\\Supports\\SettingStore.php(102): Botble\\Setting\\Supports\\DatabaseSettingStore->write(Array)
#22 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Botble\\Setting\\Supports\\SettingStore->save()
#23 C:\\Users\\<USER>\\Desktop\\uu\\fix-nagorikpay-settings.php(29): Illuminate\\Support\\Facades\\Facade::__callStatic('save', Array)
#24 {main}
"} 
