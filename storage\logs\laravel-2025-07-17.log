[2025-07-17 00:40:23] local.ERROR: Unable to locate a class or view for component [plugins-ecommerce::fronts.ajax-search.input]. (View: /var/www/html/platform/themes/farmart/partials/header.blade.php) {"exception":"[object] (Illuminate\\View\\ViewException(code: 0): Unable to locate a class or view for component [plugins-ecommerce::fronts.ajax-search.input]. (View: /var/www/html/platform/themes/farmart/partials/header.blade.php) at /var/www/html/vendor/laravel/framework/src/Illuminate/View/Compilers/ComponentTagCompiler.php:311)
[stacktrace]
#0 /var/www/html/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(60): Illuminate\\View\\Engines\\CompilerEngine->handleViewException()
#1 /var/www/html/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#2 /var/www/html/vendor/laravel/framework/src/Illuminate/View/View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get()
#3 /var/www/html/vendor/botble/shortcode/src/View/View.php(50): Illuminate\\View\\View->getContents()
#4 /var/www/html/vendor/laravel/framework/src/Illuminate/View/View.php(159): Botble\\Shortcode\\View\\View->renderContents()
#5 /var/www/html/vendor/botble/theme/src/Theme.php(792): Illuminate\\View\\View->render()
#6 /var/www/html/vendor/botble/theme/src/Http/Controllers/PublicController.php(38): Botble\\Theme\\Theme->render()
#7 /var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): Botble\\Theme\\Http\\Controllers\\PublicController->getIndex()
#8 /var/www/html/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#9 /var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#10 /var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#11 /var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#12 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#13 /var/www/html/vendor/botble/platform/base/src/Http/Middleware/CoreMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#14 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Botble\\Base\\Http\\Middleware\\CoreMiddleware->Botble\\Base\\Http\\Middleware\\{closure}()
#15 /var/www/html/vendor/botble/platform/base/src/Http/Middleware/EnsureLicenseHasBeenActivated.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Base\\Http\\Middleware\\EnsureLicenseHasBeenActivated->handle()
#17 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /var/www/html/vendor/botble/platform/base/src/Http/Middleware/CoreMiddleware.php(18): Illuminate\\Pipeline\\Pipeline->then()
#19 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Base\\Http\\Middleware\\CoreMiddleware->handle()
#20 /var/www/html/vendor/botble/platform/base/src/Http/Middleware/HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle()
#22 /var/www/html/vendor/botble/platform/base/src/Http/Middleware/AdminLocaleMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Base\\Http\\Middleware\\AdminLocaleMiddleware->handle()
#24 /var/www/html/vendor/botble/platform/base/src/Http/Middleware/LocaleMiddleware.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Base\\Http\\Middleware\\LocaleMiddleware->handle()
#26 /var/www/html/vendor/botble/installer/src/Http/Middleware/RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle()
#28 /var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#30 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#32 /var/www/html/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#34 /var/www/html/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 /var/www/html/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#36 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#37 /var/www/html/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#39 /var/www/html/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#41 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 /var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#43 /var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#44 /var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#45 /var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#46 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#47 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#48 /var/www/html/vendor/botble/platform/js-validation/src/RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\JsValidation\\RemoteValidationMiddleware->handle()
#50 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#52 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#53 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#55 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#56 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#58 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#60 /var/www/html/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#61 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#62 /var/www/html/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#63 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#64 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#65 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#66 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#67 /var/www/html/public/index.php(24): Illuminate\\Foundation\\Http\\Kernel->handle()
#68 {main}

[previous exception] [object] (InvalidArgumentException(code: 0): Unable to locate a class or view for component [plugins-ecommerce::fronts.ajax-search.input]. at /var/www/html/vendor/laravel/framework/src/Illuminate/View/Compilers/ComponentTagCompiler.php:311)
[stacktrace]
#0 /var/www/html/vendor/laravel/framework/src/Illuminate/View/Compilers/ComponentTagCompiler.php(235): Illuminate\\View\\Compilers\\ComponentTagCompiler->componentClass()
#1 /var/www/html/vendor/laravel/framework/src/Illuminate/View/Compilers/ComponentTagCompiler.php(220): Illuminate\\View\\Compilers\\ComponentTagCompiler->componentString()
#2 [internal function]: Illuminate\\View\\Compilers\\ComponentTagCompiler->Illuminate\\View\\Compilers\\{closure}()
#3 /var/www/html/vendor/laravel/framework/src/Illuminate/View/Compilers/ComponentTagCompiler.php(215): preg_replace_callback()
#4 /var/www/html/vendor/laravel/framework/src/Illuminate/View/Compilers/ComponentTagCompiler.php(89): Illuminate\\View\\Compilers\\ComponentTagCompiler->compileSelfClosingTags()
#5 /var/www/html/vendor/laravel/framework/src/Illuminate/View/Compilers/ComponentTagCompiler.php(76): Illuminate\\View\\Compilers\\ComponentTagCompiler->compileTags()
#6 /var/www/html/vendor/laravel/framework/src/Illuminate/View/Compilers/BladeCompiler.php(438): Illuminate\\View\\Compilers\\ComponentTagCompiler->compile()
#7 /var/www/html/vendor/laravel/framework/src/Illuminate/View/Compilers/BladeCompiler.php(271): Illuminate\\View\\Compilers\\BladeCompiler->compileComponentTags()
#8 /var/www/html/vendor/laravel/framework/src/Illuminate/View/Compilers/BladeCompiler.php(184): Illuminate\\View\\Compilers\\BladeCompiler->compileString()
#9 /var/www/html/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(64): Illuminate\\View\\Compilers\\BladeCompiler->compile()
#10 /var/www/html/vendor/laravel/framework/src/Illuminate/View/View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get()
#11 /var/www/html/vendor/botble/shortcode/src/View/View.php(50): Illuminate\\View\\View->getContents()
#12 /var/www/html/vendor/laravel/framework/src/Illuminate/View/View.php(159): Botble\\Shortcode\\View\\View->renderContents()
#13 /var/www/html/vendor/botble/theme/src/Theme.php(497): Illuminate\\View\\View->render()
#14 /var/www/html/vendor/botble/theme/src/Theme.php(483): Botble\\Theme\\Theme->loadPartial()
#15 /var/www/html/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(355): Botble\\Theme\\Theme->partial()
#16 /var/www/html/storage/framework/views/750a4c44e92272c75f68288d2560591f.php(1): Illuminate\\Support\\Facades\\Facade::__callStatic()
#17 /var/www/html/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(123): require('...')
#18 /var/www/html/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#19 /var/www/html/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire()
#20 /var/www/html/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#21 /var/www/html/vendor/laravel/framework/src/Illuminate/View/View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get()
#22 /var/www/html/vendor/botble/shortcode/src/View/View.php(50): Illuminate\\View\\View->getContents()
#23 /var/www/html/vendor/laravel/framework/src/Illuminate/View/View.php(159): Botble\\Shortcode\\View\\View->renderContents()
#24 /var/www/html/vendor/botble/theme/src/Theme.php(792): Illuminate\\View\\View->render()
#25 /var/www/html/vendor/botble/theme/src/Http/Controllers/PublicController.php(38): Botble\\Theme\\Theme->render()
#26 /var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): Botble\\Theme\\Http\\Controllers\\PublicController->getIndex()
#27 /var/www/html/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#28 /var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#29 /var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#30 /var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#31 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#32 /var/www/html/vendor/botble/platform/base/src/Http/Middleware/CoreMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Botble\\Base\\Http\\Middleware\\CoreMiddleware->Botble\\Base\\Http\\Middleware\\{closure}()
#34 /var/www/html/vendor/botble/platform/base/src/Http/Middleware/EnsureLicenseHasBeenActivated.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Base\\Http\\Middleware\\EnsureLicenseHasBeenActivated->handle()
#36 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 /var/www/html/vendor/botble/platform/base/src/Http/Middleware/CoreMiddleware.php(18): Illuminate\\Pipeline\\Pipeline->then()
#38 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Base\\Http\\Middleware\\CoreMiddleware->handle()
#39 /var/www/html/vendor/botble/platform/base/src/Http/Middleware/HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle()
#41 /var/www/html/vendor/botble/platform/base/src/Http/Middleware/AdminLocaleMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Base\\Http\\Middleware\\AdminLocaleMiddleware->handle()
#43 /var/www/html/vendor/botble/platform/base/src/Http/Middleware/LocaleMiddleware.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Base\\Http\\Middleware\\LocaleMiddleware->handle()
#45 /var/www/html/vendor/botble/installer/src/Http/Middleware/RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle()
#47 /var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#49 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#51 /var/www/html/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#53 /var/www/html/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 /var/www/html/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#55 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#56 /var/www/html/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#58 /var/www/html/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#60 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#61 /var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#62 /var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#63 /var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#64 /var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#65 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#66 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#67 /var/www/html/vendor/botble/platform/js-validation/src/RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#68 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Botble\\JsValidation\\RemoteValidationMiddleware->handle()
#69 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#70 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#71 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#72 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#73 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#74 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#75 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#76 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#77 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#78 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#79 /var/www/html/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#80 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#81 /var/www/html/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#82 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#83 /var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#84 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#85 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#86 /var/www/html/public/index.php(24): Illuminate\\Foundation\\Http\\Kernel->handle()
#87 {main}
"} 
[2025-07-17 00:47:38] local.ERROR: Class "Botble\Ecommerce\Forms\Fronts\Auth\RegisterForm" not found {"exception":"[object] (Error(code: 0): Class \"Botble\\Ecommerce\\Forms\\Fronts\\Auth\\RegisterForm\" not found at /var/www/html/platform/plugins/marketplace/src/Providers/HookServiceProvider.php:273)
[stacktrace]
#0 /var/www/html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Botble\\Marketplace\\Providers\\HookServiceProvider->boot()
#1 /var/www/html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 /var/www/html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#3 /var/www/html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()
#4 /var/www/html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#5 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1034): Illuminate\\Container\\Container->call()
#6 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1015): Illuminate\\Foundation\\Application->bootProvider()
#7 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#8 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1016): array_walk()
#9 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#10 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(263): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#11 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith()
#12 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#13 /var/www/html/artisan(17): Illuminate\\Foundation\\Console\\Kernel->handle()
#14 {main}
"} 
[2025-07-17 00:53:29] local.ERROR: Trait "Botble\Ecommerce\Traits\LocationTrait" not found {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Trait \"Botble\\Ecommerce\\Traits\\LocationTrait\" not found at /var/www/html/platform/plugins/marketplace/src/Models/Store.php:24)
[stacktrace]
#0 {main}
"} 
[2025-07-17 00:59:59] local.ERROR: Class "Botble\Ecommerce\Models\Customer" not found {"exception":"[object] (Error(code: 0): Class \"Botble\\Ecommerce\\Models\\Customer\" not found at /var/www/html/platform/plugins/marketplace/src/Providers/MarketplaceServiceProvider.php:345)
[stacktrace]
#0 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1077): Botble\\Marketplace\\Providers\\MarketplaceServiceProvider->Botble\\Marketplace\\Providers\\{closure}()
#1 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1020): Illuminate\\Foundation\\Application->fireAppCallbacks()
#2 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#3 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(263): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#4 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith()
#5 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#6 /var/www/html/artisan(17): Illuminate\\Foundation\\Console\\Kernel->handle()
#7 {main}
"} 
[2025-07-17 01:09:59] local.ERROR: Class "Botble\Ecommerce\Models\Customer" not found {"exception":"[object] (Error(code: 0): Class \"Botble\\Ecommerce\\Models\\Customer\" not found at /var/www/html/platform/plugins/marketplace/src/Providers/MarketplaceServiceProvider.php:369)
[stacktrace]
#0 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1077): Botble\\Marketplace\\Providers\\MarketplaceServiceProvider->Botble\\Marketplace\\Providers\\{closure}()
#1 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1020): Illuminate\\Foundation\\Application->fireAppCallbacks()
#2 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#3 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(263): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#4 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith()
#5 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#6 /var/www/html/artisan(17): Illuminate\\Foundation\\Console\\Kernel->handle()
#7 {main}
"} 
