<?php

namespace <PERSON><PERSON><PERSON>\Ecommerce\Tables;

use Bo<PERSON>ble\Ecommerce\Models\SpecificationAttribute;
use Bo<PERSON>ble\Table\Abstracts\TableAbstract;
use Bo<PERSON>ble\Table\Actions\DeleteAction;
use Bo<PERSON>ble\Table\Actions\EditAction;
use <PERSON><PERSON>ble\Table\Columns\CreatedAtColumn;
use Botble\Table\Columns\FormattedColumn;
use Botble\Table\Columns\IdColumn;
use Botble\Table\Columns\NameColumn;
use Botble\Table\HeaderActions\CreateHeaderAction;

class SpecificationAttributeTable extends TableAbstract
{
    public function setup(): void
    {
        $this
            ->model(SpecificationAttribute::class)
            ->addHeaderAction(CreateHeaderAction::make()->route('ecommerce.specification-attributes.create'))
            ->addColumns([
                IdColumn::make(),
                NameColumn::make()->route('ecommerce.specification-attributes.edit'),
                FormattedColumn::make('description')
                    ->label(trans('core/base::forms.description'))
                    ->withEmptyState()
                    ->limit(50),
                CreatedAtColumn::make(),
            ])
            ->addActions([
                EditAction::make()->route('ecommerce.specification-attributes.edit'),
                DeleteAction::make()->route('ecommerce.specification-attributes.destroy'),
            ]);
    }
}
