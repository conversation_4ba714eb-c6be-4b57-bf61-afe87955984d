<?php

namespace Bo<PERSON><PERSON>\Ecommerce\Jobs;

use <PERSON><PERSON>ble\Base\Facades\EmailHandler;
use Bo<PERSON>ble\Ecommerce\Events\AccountDeletedEvent;
use Bo<PERSON>ble\Ecommerce\Events\AccountDeletingEvent;
use Bo<PERSON>ble\Ecommerce\Models\Customer;
use Bo<PERSON>ble\Ecommerce\Models\CustomerDeletionRequest;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class CustomerDeleteAccountJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    public function __construct(public CustomerDeletionRequest $deletionRequest)
    {
    }

    public function handle(): void
    {
        /**
         * @var Customer $customer
         */
        $customer = Customer::query()->find($this->deletionRequest->customer_id);

        if (! $customer->exists) {
            return;
        }

        AccountDeletingEvent::dispatch($this->deletionRequest);

        $name = $customer->name;
        $email = $customer->email;

        $customer->delete();

        EmailHandler::setModule(ECOMMERCE_MODULE_SCREEN_NAME)
            ->setVariableValue('customer_name', $name)
            ->sendUsingTemplate('customer-deletion-request-completed', $email);

        AccountDeletedEvent::dispatch($email, $name, $customer);
    }
}
