<?php

namespace Botble\NagorikPay\Http\Controllers;

use Botble\Base\Http\Controllers\BaseController;
use Botble\NagorikPay\Services\NagorikPayService;
use Botble\Payment\Enums\PaymentStatusEnum;
use Botble\Payment\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Log;

class NagorikPayController extends BaseController
{
    protected NagorikPayService $nagorikPayService;

    public function __construct(NagorikPayService $nagorikPayService)
    {
        $this->nagorikPayService = $nagorikPayService;
    }

    public function success(Request $request): RedirectResponse
    {
        $transactionId = $request->get('transactionId');
        $paymentMethod = $request->get('paymentMethod');
        $paymentAmount = $request->get('paymentAmount');
        $paymentFee = $request->get('paymentFee');
        $status = $request->get('status');

        if (!$transactionId) {
            return redirect()->route('public.checkout.information')
                ->with('error_msg', trans('plugins/nagorikpay::nagorikpay.payment_failed'));
        }

        // Verify payment with NagorikPay
        $verification = $this->nagorikPayService->verifyPayment($transactionId);

        if ($verification['status'] && isset($verification['data']['status'])) {
            $verifiedStatus = $verification['data']['status'];
            
            // Update payment status
            $this->nagorikPayService->updatePaymentStatus($transactionId, $verifiedStatus, [
                'payment_method' => $paymentMethod,
                'payment_fee' => $paymentFee
            ]);

            if (strtoupper($verifiedStatus) === 'COMPLETED') {
                return redirect()->route('public.checkout.success')
                    ->with('success_msg', trans('plugins/nagorikpay::nagorikpay.payment_success'));
            }
        }

        return redirect()->route('public.checkout.information')
            ->with('error_msg', trans('plugins/nagorikpay::nagorikpay.payment_failed'));
    }

    public function cancel(Request $request): RedirectResponse
    {
        $transactionId = $request->get('transactionId');
        
        if ($transactionId) {
            $this->nagorikPayService->updatePaymentStatus($transactionId, 'FAILED');
        }

        return redirect()->route('public.checkout.information')
            ->with('error_msg', trans('plugins/nagorikpay::nagorikpay.payment_cancelled'));
    }

    public function webhook(Request $request): JsonResponse
    {
        try {
            $data = $request->all();
            
            Log::info('NagorikPay webhook received:', $data);

            if (isset($data['transaction_id']) && isset($data['status'])) {
                $this->nagorikPayService->updatePaymentStatus(
                    $data['transaction_id'], 
                    $data['status'],
                    $data
                );
            }

            return response()->json(['status' => 'success']);

        } catch (\Exception $e) {
            Log::error('NagorikPay webhook error: ' . $e->getMessage());
            return response()->json(['status' => 'error'], 500);
        }
    }
}
