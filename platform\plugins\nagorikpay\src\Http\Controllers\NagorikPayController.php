<?php

namespace Botble\NagorikPay\Http\Controllers;

use Botble\Base\Http\Controllers\BaseController;
use Botble\Payment\Enums\PaymentStatusEnum;
use Botble\Payment\Models\Payment;
use Botble\NagorikPay\Services\NagorikPayService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class NagorikPayController extends BaseController
{
    protected NagorikPayService $nagorikPayService;

    public function __construct(NagorikPayService $nagorikPayService)
    {
        $this->nagorikPayService = $nagorikPayService;
    }

    public function webhook(Request $request)
    {
        try {
            // Get parameters from request (similar to WHMCS callback)
            $orderId = $request->get('order');
            $transactionId = $request->get('transactionId');
            $paymentAmount = $request->get('paymentAmount');
            $paymentFee = $request->get('paymentFee', 0);
            $apiKey = $request->get('api');

            // Validate API key
            $configApiKey = get_payment_setting('api_key', NAGORIKPAY_PAYMENT_METHOD_NAME, '');
            if ($apiKey !== $configApiKey) {
                Log::error('NagorikPay webhook: Invalid API key');
                return response('Invalid API key', 401);
            }

            // Verify payment with NagorikPay (like WHMCS does)
            $verification = $this->nagorikPayService->verifyPayment($transactionId);

            if ($verification['success'] && $verification['status'] === 'COMPLETED') {
                // Find and update payment
                $payment = Payment::where('order_id', $orderId)->first();
                
                if ($payment) {
                    $payment->status = PaymentStatusEnum::COMPLETED;
                    $payment->charge_id = $transactionId;
                    $payment->save();

                    // Log successful payment
                    Log::info('NagorikPay payment completed', [
                        'order_id' => $orderId,
                        'transaction_id' => $transactionId,
                        'amount' => $paymentAmount
                    ]);

                    return response('Payment processed successfully', 200);
                } else {
                    Log::error('NagorikPay webhook: Payment not found', ['order_id' => $orderId]);
                    return response('Payment not found', 404);
                }
            } else {
                Log::error('NagorikPay webhook: Payment verification failed', [
                    'transaction_id' => $transactionId,
                    'verification' => $verification
                ]);
                return response('Payment verification failed', 400);
            }

        } catch (\Exception $e) {
            Log::error('NagorikPay webhook error: ' . $e->getMessage());
            return response('Webhook processing failed', 500);
        }
    }

    public function success(Request $request)
    {
        $orderId = $request->get('order');
        
        // Redirect to success page or order details
        return redirect()->route('public.checkout.success')->with('success', 'Payment completed successfully!');
    }

    public function cancel(Request $request)
    {
        $orderId = $request->get('order');
        
        // Redirect to checkout page with error message
        return redirect()->route('public.checkout.information')->with('error', 'Payment was cancelled.');
    }
}
