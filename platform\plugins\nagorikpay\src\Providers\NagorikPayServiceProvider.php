<?php

namespace Bo<PERSON>ble\NagorikPay\Providers;

use Bo<PERSON>ble\Base\Supports\ServiceProvider;
use Bo<PERSON>ble\Base\Traits\LoadAndPublishDataTrait;
use Botble\Payment\Enums\PaymentMethodEnum;
use Illuminate\Http\Request;

class NagorikPayServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;

    public function register(): void
    {
        $this->app->bind(PaymentMethodEnum::class, function () {
            return new PaymentMethodEnum();
        });
    }

    public function boot(): void
    {
        if (is_plugin_active('payment')) {
            $this->setNamespace('plugins/nagorikpay')
                ->loadHelpers()
                ->loadAndPublishConfigurations(['payment'])
                ->loadAndPublishViews()
                ->loadAndPublishTranslations()
                ->loadRoutes()
                ->publishAssets();

            $this->app->register(HookServiceProvider::class);

            add_filter(PAYMENT_FILTER_ADDITIONAL_PAYMENT_METHODS, [$this, 'registerNagorikPayMethod'], 16, 2);
            add_filter(PAYMENT_FILTER_AFTER_POST_CHECKOUT, [$this, 'checkoutWithNagorikPay'], 16, 2);

            add_filter(PAYMENT_METHODS_SETTINGS_PAGE, [$this, 'addPaymentSettings'], 99);
        }
    }

    public function registerNagorikPayMethod(?string $html, array $data): string
    {
        return $html . view('plugins/nagorikpay::methods', $data)->render();
    }

    public function checkoutWithNagorikPay(array $data, Request $request): array
    {
        if ($request->input('payment_method') !== NAGORIKPAY_PAYMENT_METHOD_NAME) {
            return $data;
        }

        $nagorikPayService = app(\Botble\NagorikPay\Services\NagorikPayService::class);

        $response = $nagorikPayService->makePayment($data);

        if ($response['status']) {
            return [
                'error' => false,
                'message' => trans('plugins/nagorikpay::nagorikpay.payment_pending'),
                'data' => [
                    'redirect_url' => $response['payment_url'],
                ],
            ];
        }

        return [
            'error' => true,
            'message' => $response['message'] ?? trans('plugins/nagorikpay::nagorikpay.payment_failed'),
        ];
    }

    public function addPaymentSettings(?string $settings): string
    {
        return $settings . view('plugins/nagorikpay::settings')->render();
    }
}
