<?php

namespace Bo<PERSON>ble\NagorikPay\Providers;

use Bo<PERSON>ble\Base\Supports\ServiceProvider;
use Bo<PERSON>ble\Base\Traits\LoadAndPublishDataTrait;
use Botble\NagorikPay\Services\NagorikPayService;
use Illuminate\Http\Request;

class NagorikPayServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;

    public function register(): void
    {
        $this->app->singleton(NagorikPayService::class, function ($app) {
            return new NagorikPayService();
        });
    }

    public function boot(): void
    {
        if (is_plugin_active('payment')) {
            $this->setNamespace('plugins/nagorikpay')
                ->loadHelpers()
                ->loadAndPublishConfigurations(['payment'])
                ->loadAndPublishViews()
                ->loadAndPublishTranslations()
                ->loadRoutes()
                ->publishAssets();

            $this->app->register(HookServiceProvider::class);

            // Use more reliable hook registration
            add_filter('payment_filter_additional_payment_methods', [$this, 'registerNagorikPayMethod'], 16, 2);
            add_filter('payment_filter_after_post_checkout', [$this, 'checkoutWithNagorikPay'], 16, 2);
            add_filter('payment_methods_settings_page', [$this, 'addPaymentSettings'], 99);

            // Add settings save handler
            add_action('core_config_saved', [$this, 'savePaymentSettings'], 99);
        }
    }

    public function registerNagorikPayMethod(?string $html, array $data): string
    {
        try {
            return $html . view('plugins/nagorikpay::methods', $data)->render();
        } catch (\Exception $e) {
            return $html;
        }
    }

    public function checkoutWithNagorikPay(array $data, Request $request): array
    {
        try {
            $methodName = defined('NAGORIKPAY_PAYMENT_METHOD_NAME') ? NAGORIKPAY_PAYMENT_METHOD_NAME : 'nagorikpay';

            if ($request->input('payment_method') !== $methodName) {
                return $data;
            }

            // Check if NagorikPay is enabled
            if (!get_payment_setting('status', $methodName)) {
                return [
                    'error' => true,
                    'message' => 'NagorikPay payment method is not enabled.',
                ];
            }

            // Check if API key is configured
            $apiKey = get_payment_setting('api_key', $methodName);
            if (empty($apiKey)) {
                return [
                    'error' => true,
                    'message' => 'NagorikPay is not properly configured. Please contact administrator.',
                ];
            }

            $nagorikPayService = app(NagorikPayService::class);
            $response = $nagorikPayService->makePayment($data);

            if (isset($response['status']) && $response['status']) {
                return [
                    'error' => false,
                    'message' => 'Redirecting to NagorikPay...',
                    'data' => [
                        'redirect_url' => $response['payment_url'],
                    ],
                ];
            }

            return [
                'error' => true,
                'message' => $response['message'] ?? 'Payment initiation failed. Please try again.',
            ];
        } catch (\Exception $e) {
            \Log::error('NagorikPay checkout error: ' . $e->getMessage());
            return [
                'error' => true,
                'message' => 'Payment service temporarily unavailable. Please try again later.',
            ];
        }
    }

    public function addPaymentSettings(?string $settings): string
    {
        try {
            return $settings . view('plugins/nagorikpay::settings')->render();
        } catch (\Exception $e) {
            return $settings;
        }
    }

    public function savePaymentSettings(): void
    {
        try {
            $request = request();

            if ($request->has('payment_nagorikpay_status')) {
                setting()->set('payment_nagorikpay_status', $request->input('payment_nagorikpay_status', 0));
            }

            if ($request->has('payment_nagorikpay_name')) {
                setting()->set('payment_nagorikpay_name', $request->input('payment_nagorikpay_name', 'NagorikPay'));
            }

            if ($request->has('payment_nagorikpay_description')) {
                setting()->set('payment_nagorikpay_description', $request->input('payment_nagorikpay_description', ''));
            }

            if ($request->has('payment_nagorikpay_api_key')) {
                setting()->set('payment_nagorikpay_api_key', $request->input('payment_nagorikpay_api_key', ''));
            }

            if ($request->has('payment_nagorikpay_mode')) {
                setting()->set('payment_nagorikpay_mode', $request->input('payment_nagorikpay_mode', 'sandbox'));
            }

            setting()->save();
        } catch (\Exception $e) {
            // Log error but don't break the settings save process
            \Log::error('NagorikPay settings save error: ' . $e->getMessage());
        }
    }
}
