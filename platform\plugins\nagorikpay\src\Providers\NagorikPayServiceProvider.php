<?php

namespace Botble\NagorikPay\Providers;

use Botble\Base\Supports\ServiceProvider;
use Botble\Base\Traits\LoadAndPublishDataTrait;

class NagorikPayServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;

    public function boot(): void
    {
        if (!is_plugin_active('payment')) {
            return;
        }

        $this->setNamespace('plugins/nagorikpay')
            ->loadHelpers()
            ->loadRoutes()
            ->loadAndPublishViews()
            ->publishAssets();

        // Only register hooks if not already registered to prevent loops
        if (!$this->app->bound('nagorikpay.hooks.registered')) {
            $this->app->singleton('nagorikpay.hooks.registered', function () {
                return true;
            });
            
            $this->app->register(HookServiceProvider::class);
        }
    }
}
