<?php

namespace Bo<PERSON><PERSON>\NagorikPay\Providers;

use Bo<PERSON>ble\Base\Supports\ServiceProvider;
use Bo<PERSON>ble\Base\Traits\LoadAndPublishDataTrait;
use Botble\NagorikPay\Services\NagorikPayService;
use Illuminate\Http\Request;

class NagorikPayServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;

    public function register(): void
    {
        $this->app->singleton(NagorikPayService::class, function ($app) {
            return new NagorikPayService();
        });
    }

    public function boot(): void
    {
        if (is_plugin_active('payment')) {
            $this->setNamespace('plugins/nagorikpay')
                ->loadHelpers()
                ->loadAndPublishConfigurations(['payment'])
                ->loadAndPublishViews()
                ->loadAndPublishTranslations()
                ->loadRoutes()
                ->publishAssets();

            $this->app->register(HookServiceProvider::class);

            if (defined('PAYMENT_FILTER_ADDITIONAL_PAYMENT_METHODS')) {
                add_filter(PAYMENT_FILTER_ADDITIONAL_PAYMENT_METHODS, [$this, 'registerNagorikPayMethod'], 16, 2);
            }

            if (defined('PAYMENT_FILTER_AFTER_POST_CHECKOUT')) {
                add_filter(PAYMENT_FILTER_AFTER_POST_CHECKOUT, [$this, 'checkoutWithNagorikPay'], 16, 2);
            }

            if (defined('PAYMENT_METHODS_SETTINGS_PAGE')) {
                add_filter(PAYMENT_METHODS_SETTINGS_PAGE, [$this, 'addPaymentSettings'], 99);
            }
        }
    }

    public function registerNagorikPayMethod(?string $html, array $data): string
    {
        try {
            return $html . view('plugins/nagorikpay::methods', $data)->render();
        } catch (\Exception $e) {
            return $html;
        }
    }

    public function checkoutWithNagorikPay(array $data, Request $request): array
    {
        try {
            if ($request->input('payment_method') !== NAGORIKPAY_PAYMENT_METHOD_NAME) {
                return $data;
            }

            $nagorikPayService = app(NagorikPayService::class);
            $response = $nagorikPayService->makePayment($data);

            if (isset($response['status']) && $response['status']) {
                return [
                    'error' => false,
                    'message' => trans('plugins/nagorikpay::nagorikpay.payment_pending'),
                    'data' => [
                        'redirect_url' => $response['payment_url'],
                    ],
                ];
            }

            return [
                'error' => true,
                'message' => $response['message'] ?? trans('plugins/nagorikpay::nagorikpay.payment_failed'),
            ];
        } catch (\Exception $e) {
            return [
                'error' => true,
                'message' => 'Payment service error: ' . $e->getMessage(),
            ];
        }
    }

    public function addPaymentSettings(?string $settings): string
    {
        try {
            return $settings . view('plugins/nagorikpay::settings')->render();
        } catch (\Exception $e) {
            return $settings;
        }
    }
}
