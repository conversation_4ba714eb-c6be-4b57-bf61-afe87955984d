# NagorikPay Plugin Installation Guide

## Overview
The NagorikPay plugin has been created and is ready for activation on your live site. This plugin integrates the NagorikPay payment gateway with your Laravel ecommerce application.

## Plugin Structure
```
platform/plugins/nagorikpay/
├── plugin.json                 # Plugin configuration
├── README.md                   # Plugin documentation
├── src/
│   ├── Plugin.php              # Plugin lifecycle hooks
│   ├── Http/Controllers/       # Payment controllers
│   ├── Providers/              # Service providers
│   └── Services/               # Payment service logic
├── config/
│   └── payment.php             # Payment configuration
├── resources/
│   ├── views/                  # Blade templates
│   └── lang/                   # Language files
├── routes/
│   └── web.php                 # Plugin routes
├── public/
│   └── images/                 # Plugin assets
├── database/
│   └── migrations/             # Database migrations
└── helpers/
    └── constants.php           # Plugin constants
```

## How to Activate

### Method 1: Admin Panel (Recommended)
1. Login to your admin panel
2. Go to **Plugins** section
3. Find **NagorikPay** in the plugin list
4. Click **Activate**

### Method 2: Command Line
```bash
php artisan cms:plugin:activate nagorikpay
```

## Configuration Steps

### 1. Get NagorikPay API Key
- Sign up at https://nagorikpay.com
- Get your API key from the dashboard
- Note: Use your API key: `gnXi7etgWNhFyFGZFrOMYyrmnF4A1eGU5SC2QRmUvILOlNc2Ef`

### 2. Configure Plugin Settings
1. Go to **Admin Panel → Settings → Payment Methods**
2. Find **NagorikPay** section
3. Configure the following:
   - **Status**: Enable/Disable the payment method
   - **Name**: Display name (default: "NagorikPay")
   - **Description**: Payment method description
   - **API Key**: Your NagorikPay API key
   - **Mode**: Choose "Sandbox" for testing or "Live" for production

### 3. Test the Integration
1. Set mode to "Sandbox"
2. Make a test purchase
3. Verify payment flow works correctly
4. Check webhook responses
5. Switch to "Live" mode when ready

## Features Included

✅ **Payment Processing**: Create payments via NagorikPay API  
✅ **Payment Verification**: Verify payment status  
✅ **Webhook Support**: Automatic payment status updates  
✅ **Multiple Payment Methods**: bKash, Nagad, Rocket, etc.  
✅ **Sandbox/Live Modes**: Testing and production environments  
✅ **Admin Configuration**: Easy setup through admin panel  
✅ **Error Handling**: Comprehensive error management  
✅ **Logging**: Payment activity logging  
✅ **Multi-language**: English language support (extensible)  

## API Integration Details

### Payment Creation
- **Endpoint**: `https://secure-pay.nagorikpay.com/api/payment/create`
- **Method**: POST
- **Headers**: API-KEY, Content-Type: application/json

### Payment Verification
- **Endpoint**: `https://secure-pay.nagorikpay.com/api/payment/verify`
- **Method**: POST
- **Headers**: API-KEY, Content-Type: application/json

### Webhook URLs
The plugin automatically sets up these webhook URLs:
- **Success**: `https://yourdomain.com/payments/nagorikpay/success`
- **Cancel**: `https://yourdomain.com/payments/nagorikpay/cancel`
- **Webhook**: `https://yourdomain.com/payments/nagorikpay/webhook`

## Troubleshooting

### Common Issues
1. **Plugin not showing**: Clear cache and refresh plugins list
2. **API errors**: Verify API key is correct
3. **Webhook not working**: Check URL accessibility
4. **Payment not completing**: Check logs for errors

### Debug Steps
1. Enable debug mode in Laravel
2. Check `storage/logs/laravel.log` for errors
3. Verify API key configuration
4. Test webhook URL accessibility

## Support

- **Plugin Issues**: Contact your development team
- **NagorikPay API**: Contact NagorikPay support
- **Documentation**: https://nagorikpay.com

## Security Notes

- Keep API keys secure and never expose them publicly
- Use HTTPS for all payment-related pages
- Regularly update the plugin for security patches
- Monitor payment logs for suspicious activity

The plugin is now ready for activation and use on your live site!
