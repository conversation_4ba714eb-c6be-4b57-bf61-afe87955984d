@php
    $nagorikpayMethodName = defined('NAGORIKPAY_PAYMENT_METHOD_NAME') ? NAGORIKPAY_PAYMENT_METHOD_NAME : 'nagorikpay';
    $nagorikpayStatus = get_payment_setting('status', $nagorikpayMethodName, 0);
@endphp

<table class="table payment-method-item">
    <tbody>
    <tr class="border-pay-row">
        <td class="border-pay-col"><i class="fa fa-theme-payments"></i></td>
        <td style="width: 20%;">
            <img class="filter-black" src="{{ url('vendor/core/plugins/nagorikpay/images/nagorikpay.svg') }}" alt="NagorikPay" style="height: 24px;">
        </td>
        <td class="border-right">
            <ul>
                <li>
                    <label><strong>{{ trans('plugins/nagorikpay::nagorikpay.settings.title') }}</strong></label>
                </li>
                <li>
                    <span>{{ trans('plugins/nagorikpay::nagorikpay.settings.description') }}</span>
                </li>
            </ul>
        </td>
    </tr>
    <tr class="bg-white">
        <td colspan="3">
            <div class="float-start" style="margin-top: 5px;">
                <div class="payment-name-label-group @if (get_payment_setting('status', $nagorikpayMethodName) == 0) hidden @endif">
                    <span class="payment-note v-a-t">{{ trans('plugins/payment::payment.use') }}:</span> <label class="ws-nm inline-display method-name-label">{{ get_payment_setting('name', $nagorikpayMethodName, trans('plugins/nagorikpay::nagorikpay.method_name')) }}</label>
                </div>
            </div>
            <div class="float-end">
                <label class="me-2">
                    <input type="radio" name="payment_nagorikpay_status" value="1" @if ($nagorikpayStatus == 1) checked @endif class="setting-selection-option" data-target="#nagorikpay_settings">
                    {{ trans('core/setting::setting.general.yes') }}
                </label>
                <label>
                    <input type="radio" name="payment_nagorikpay_status" value="0" @if ($nagorikpayStatus == 0) checked @endif class="setting-selection-option" data-target="#nagorikpay_settings">
                    {{ trans('core/setting::setting.general.no') }}
                </label>
            </div>
        </td>
    </tr>
    <tr class="nagorikpay_settings payment-content-item hidden @if (get_payment_setting('status', $nagorikpayMethodName) == 1) show @endif">
        <td class="border-left" colspan="3">
            <div class="payment-note-box">
                <div class="row">
                    <div class="col-sm-6">
                        <div class="well bg-white">
                            <div class="form-group mb-3">
                                <label class="text-title-field" for="payment_nagorikpay_name">{{ trans('plugins/nagorikpay::nagorikpay.settings.name') }}</label>
                                <input type="text" class="next-input payment-method-name" name="payment_nagorikpay_name" id="payment_nagorikpay_name"
                                       data-counter="400" value="{{ get_payment_setting('name', $nagorikpayMethodName, trans('plugins/nagorikpay::nagorikpay.method_name')) }}"
                                       placeholder="{{ trans('plugins/nagorikpay::nagorikpay.method_name') }}">
                            </div>
                            <div class="form-group mb-3">
                                <label class="text-title-field" for="payment_nagorikpay_description">{{ trans('plugins/nagorikpay::nagorikpay.settings.description') }}</label>
                                <textarea class="next-input" name="payment_nagorikpay_description" id="payment_nagorikpay_description">{{ get_payment_setting('description', $nagorikpayMethodName, trans('plugins/nagorikpay::nagorikpay.payment_description')) }}</textarea>
                            </div>
                            <div class="form-group mb-3">
                                <label class="text-title-field" for="payment_nagorikpay_api_key">{{ trans('plugins/nagorikpay::nagorikpay.settings.api_key') }}</label>
                                <input type="text" class="next-input" name="payment_nagorikpay_api_key" id="payment_nagorikpay_api_key"
                                       value="{{ get_payment_setting('api_key', $nagorikpayMethodName) }}"
                                       placeholder="{{ trans('plugins/nagorikpay::nagorikpay.settings.api_key_placeholder') }}">
                            </div>
                            <div class="form-group mb-3">
                                <label class="text-title-field" for="payment_nagorikpay_mode">{{ trans('plugins/nagorikpay::nagorikpay.settings.mode') }}</label>
                                <select class="next-input" name="payment_nagorikpay_mode" id="payment_nagorikpay_mode">
                                    <option value="sandbox" @if (get_payment_setting('mode', $nagorikpayMethodName, 'sandbox') === 'sandbox') selected @endif>
                                        {{ trans('plugins/nagorikpay::nagorikpay.settings.sandbox') }}
                                    </option>
                                    <option value="live" @if (get_payment_setting('mode', $nagorikpayMethodName, 'sandbox') === 'live') selected @endif>
                                        {{ trans('plugins/nagorikpay::nagorikpay.settings.live') }}
                                    </option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="well bg-white">
                            <div class="form-group mb-3">
                                <p><strong>{{ trans('plugins/nagorikpay::nagorikpay.settings.title') }}</strong></p>
                                <p>{{ trans('plugins/nagorikpay::nagorikpay.payment_description') }}</p>
                                <p><strong>API Key:</strong> gnXi7etgWNhFyFGZFrOMYyrmnF4A1eGU5SC2QRmUvILOlNc2Ef</p>
                                <p><strong>Supported Methods:</strong> bKash, Nagad, Rocket, Bank Transfer</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </td>
    </tr>
    </tbody>
</table>
