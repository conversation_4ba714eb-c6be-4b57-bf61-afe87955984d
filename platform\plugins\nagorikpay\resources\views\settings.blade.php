@php
    $nagorikpayMethodName = defined('NAGORIKPAY_PAYMENT_METHOD_NAME') ? NAGORIKPAY_PAYMENT_METHOD_NAME : 'nagorikpay';
    $nagorikpayStatus = get_payment_setting('status', $nagorikpayMethodName, 0);
@endphp

<div class="flexbox-annotated-section">
    <div class="flexbox-annotated-section-annotation">
        <div class="annotated-section-title pd-all-20">
            <h2>{{ trans('plugins/nagorikpay::nagorikpay.settings.title') }}</h2>
        </div>
        <div class="annotated-section-description pd-all-20 p-none-t">
            <p class="color-note">{{ trans('plugins/nagorikpay::nagorikpay.settings.description') }}</p>
        </div>
    </div>

    <div class="flexbox-annotated-section-content">
        <div class="wrapper-content pd-all-20">
            <div class="form-group mb-3">
                <label class="text-title-field">{{ trans('plugins/nagorikpay::nagorikpay.settings.activate') }}</label>
                <label class="me-2">
                    <input type="radio" name="payment_nagorikpay_status" value="1" @if ($nagorikpayStatus) checked @endif class="setting-selection-option" data-target="#nagorikpay_settings">
                    {{ trans('core/setting::setting.general.yes') }}
                </label>
                <label>
                    <input type="radio" name="payment_nagorikpay_status" value="0" @if (!$nagorikpayStatus) checked @endif class="setting-selection-option" data-target="#nagorikpay_settings">
                    {{ trans('core/setting::setting.general.no') }}
                </label>
            </div>

            <div id="nagorikpay_settings" class="payment-name-setting @if (!$nagorikpayStatus) d-none @endif">
                <div class="form-group mb-3">
                    <label class="text-title-field" for="payment_nagorikpay_name">{{ trans('plugins/nagorikpay::nagorikpay.settings.name') }}</label>
                    <input type="text" class="next-input" name="payment_nagorikpay_name" id="payment_nagorikpay_name"
                           value="{{ get_payment_setting('name', $nagorikpayMethodName, trans('plugins/nagorikpay::nagorikpay.method_name')) }}"
                           placeholder="{{ trans('plugins/nagorikpay::nagorikpay.method_name') }}">
                </div>

                <div class="form-group mb-3">
                    <label class="text-title-field" for="payment_nagorikpay_description">{{ trans('plugins/nagorikpay::nagorikpay.settings.description') }}</label>
                    <textarea class="next-input" name="payment_nagorikpay_description" id="payment_nagorikpay_description">{{ get_payment_setting('description', $nagorikpayMethodName, trans('plugins/nagorikpay::nagorikpay.payment_description')) }}</textarea>
                </div>

                <div class="form-group mb-3">
                    <label class="text-title-field" for="payment_nagorikpay_api_key">{{ trans('plugins/nagorikpay::nagorikpay.settings.api_key') }}</label>
                    <input type="text" class="next-input" name="payment_nagorikpay_api_key" id="payment_nagorikpay_api_key"
                           value="{{ get_payment_setting('api_key', $nagorikpayMethodName) }}"
                           placeholder="{{ trans('plugins/nagorikpay::nagorikpay.settings.api_key_placeholder') }}">
                </div>

                <div class="form-group mb-3">
                    <label class="text-title-field" for="payment_nagorikpay_mode">{{ trans('plugins/nagorikpay::nagorikpay.settings.mode') }}</label>
                    <select class="next-input" name="payment_nagorikpay_mode" id="payment_nagorikpay_mode">
                        <option value="sandbox" @if (get_payment_setting('mode', $nagorikpayMethodName, 'sandbox') === 'sandbox') selected @endif>
                            {{ trans('plugins/nagorikpay::nagorikpay.settings.sandbox') }}
                        </option>
                        <option value="live" @if (get_payment_setting('mode', $nagorikpayMethodName, 'sandbox') === 'live') selected @endif>
                            {{ trans('plugins/nagorikpay::nagorikpay.settings.live') }}
                        </option>
                    </select>
                </div>
            </div>
        </div>
    </div>
</div>
