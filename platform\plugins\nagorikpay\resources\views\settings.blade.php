@php
    $methodName = defined('NAGORIKPAY_PAYMENT_METHOD_NAME') ? NAGORIKPAY_PAYMENT_METHOD_NAME : 'nagorikpay';
@endphp

<table class="table payment-method-item">
    <tbody>
    <tr class="border-pay-row">
        <td class="border-pay-col">
            <i class="fa fa-theme-payments"></i>
        </td>
        <td style="width: 20%;">
            <img class="filter-black" src="{{ url('vendor/core/plugins/nagorikpay/images/nagorikpay.png') }}" alt="NagorikPay">
        </td>
        <td class="border-right">
            <ul>
                <li>
                    <a href="https://nagorikpay.com" target="_blank">NagorikPay</a>
                    <p>Customer can buy product and pay with bKash, Nagad, Rocket and other popular payment methods in Bangladesh via NagorikPay</p>
                </li>
            </ul>
        </td>
    </tr>
    <tr class="bg-white">
        <td colspan="3">
            <div class="float-start" style="margin-top: 5px;">
                <div class="payment-name-label-group @if (get_payment_setting('status', $methodName) == 0) hidden @endif">
                    <span class="payment-note v-a-t">{{ trans('plugins/payment::payment.use') }}:</span> <label class="ws-nm inline-display method-name-label">{{ get_payment_setting('name', $methodName) }}</label>
                </div>
            </div>
            <div class="float-end">
                <label class="me-2">{{ trans('plugins/payment::payment.status') }}</label>
                <label class="me-2">
                    <input type="radio" name="payment_{{ $methodName }}_status"
                           value="1"
                           @if (get_payment_setting('status', $methodName) == 1) checked @endif
                           class="toggler-method-option">
                    {{ trans('plugins/payment::payment.activate') }}
                </label>
                <label>
                    <input type="radio" name="payment_{{ $methodName }}_status"
                           value="0"
                           @if (get_payment_setting('status', $methodName) == 0) checked @endif
                           class="toggler-method-option">
                    {{ trans('plugins/payment::payment.deactivate') }}
                </label>
            </div>
        </td>
    </tr>
    <tr class="payment-content-item hidden">
        <td class="border-left" colspan="3">
            {!! Form::open() !!}
            {!! Form::hidden('type', $methodName, ['class' => 'payment_type']) !!}
            <div class="row">
                <div class="col-sm-6">
                    <div class="form-group mb-3">
                        <label class="text-title-field"
                               for="{{ $methodName }}_name">{{ trans('plugins/payment::payment.method_name') }}</label>
                        <input type="text" class="next-input" name="payment_{{ $methodName }}_name" id="{{ $methodName }}_name"
                               data-counter="400"
                               value="{{ get_payment_setting('name', $methodName, 'NagorikPay') }}">
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group mb-3">
                        <label class="text-title-field"
                               for="{{ $methodName }}_description">{{ trans('core/base::forms.description') }}</label>
                        <textarea class="next-input" name="payment_{{ $methodName }}_description" id="{{ $methodName }}_description">{{ get_payment_setting('description', $methodName, 'Pay securely with NagorikPay - supports bKash, Nagad, Rocket and other popular payment methods in Bangladesh') }}</textarea>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-6">
                    <div class="form-group mb-3">
                        <label class="text-title-field required"
                               for="{{ $methodName }}_api_key">{{ __('API Key') }}</label>
                        <input type="text" class="next-input" placeholder="{{ __('Enter your NagorikPay API Key') }}" name="payment_{{ $methodName }}_api_key" id="{{ $methodName }}_api_key"
                               value="{{ get_payment_setting('api_key', $methodName, 'gnXi7etgWNhFyFGZFrOMYyrmnF4A1eGU5SC2QRmUvILOlNc2Ef') }}">
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group mb-3">
                        <label class="text-title-field required"
                               for="{{ $methodName }}_currency_rate">{{ __('Currency Rate') }}</label>
                        <input type="number" step="0.01" class="next-input" placeholder="{{ __('Enter Dollar Rate (e.g., 85)') }}" name="payment_{{ $methodName }}_currency_rate" id="{{ $methodName }}_currency_rate"
                               value="{{ get_payment_setting('currency_rate', $methodName, '85') }}">
                        <span class="help-block">{{ __('Rate to convert USD to BDT (if applicable)') }}</span>
                    </div>
                </div>
            </div>
            
            <div class="form-group mb-3">
                <p class="payment-note">
                    {{ __('To use NagorikPay, you need to:') }}
                </p>
                <ul class="m-md-l" style="list-style-type: decimal">
                    <li style="list-style-type: decimal">
                        <p>{{ __('Go to') }} <a href="https://nagorikpay.com" target="_blank">https://nagorikpay.com</a> {{ __('to register an account.') }}</p>
                    </li>
                    <li style="list-style-type: decimal">
                        <p>{{ __('After registration at NagorikPay, you will have API Key') }}</p>
                    </li>
                    <li style="list-style-type: decimal">
                        <p>{{ __('Enter API Key above.') }}</p>
                    </li>
                    <li style="list-style-type: decimal">
                        <p>{{ __('Set currency rate for USD to BDT conversion (if needed).') }}</p>
                    </li>
                </ul>
                <p>
                    <strong>{{ __('Current API Key:') }}</strong> {{ get_payment_setting('api_key', $methodName, 'Not Set') }}
                </p>
                <p>
                    <strong>{{ __('Current Currency Rate:') }}</strong> {{ get_payment_setting('currency_rate', $methodName, '85') }}
                </p>
            </div>
            {!! Form::close() !!}
        </td>
    </tr>
    </tbody>
</table>
