<x-core-setting::section
    :title="trans('plugins/nagorikpay::nagorikpay.settings.title')"
    :description="trans('plugins/nagorikpay::nagorikpay.settings.description')"
>
    <x-core-setting::on-off
        name="payment_nagorikpay_status"
        :label="trans('plugins/nagorikpay::nagorikpay.settings.activate')"
        :value="get_payment_setting('status', NAGORIKPAY_PAYMENT_METHOD_NAME)"
        class="setting-selection-option"
        data-target="#nagorikpay_settings"
    />

    <div id="nagorikpay_settings" class="payment-name-setting @if (get_payment_setting('status', NAGORIKPAY_PAYMENT_METHOD_NAME) == 0) d-none @endif">
        <x-core-setting::text-input
            name="payment_nagorikpay_name"
            :label="trans('plugins/nagorikpay::nagorikpay.settings.name')"
            :value="get_payment_setting('name', NAGORIKPAY_PAYMENT_METHOD_NAME, trans('plugins/nagorikpay::nagorikpay.method_name'))"
            :placeholder="trans('plugins/nagorikpay::nagorikpay.method_name')"
        />

        <x-core-setting::form-group>
            <label class="text-title-field" for="payment_nagorikpay_description">{{ trans('plugins/nagorikpay::nagorikpay.settings.description') }}</label>
            <textarea class="next-input" name="payment_nagorikpay_description" id="payment_nagorikpay_description">{{ get_payment_setting('description', NAGORIKPAY_PAYMENT_METHOD_NAME, trans('plugins/nagorikpay::nagorikpay.payment_description')) }}</textarea>
        </x-core-setting::form-group>

        <x-core-setting::text-input
            name="payment_nagorikpay_api_key"
            :label="trans('plugins/nagorikpay::nagorikpay.settings.api_key')"
            :value="get_payment_setting('api_key', NAGORIKPAY_PAYMENT_METHOD_NAME)"
            :placeholder="trans('plugins/nagorikpay::nagorikpay.settings.api_key_placeholder')"
        />

        <x-core-setting::select
            name="payment_nagorikpay_mode"
            :label="trans('plugins/nagorikpay::nagorikpay.settings.mode')"
            :options="[
                'sandbox' => trans('plugins/nagorikpay::nagorikpay.settings.sandbox'),
                'live' => trans('plugins/nagorikpay::nagorikpay.settings.live')
            ]"
            :value="get_payment_setting('mode', NAGORIKPAY_PAYMENT_METHOD_NAME, 'sandbox')"
        />
    </div>
</x-core-setting::section>
