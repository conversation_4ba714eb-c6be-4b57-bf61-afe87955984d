<?php

namespace Bo<PERSON>ble\NagorikPay;

use Bo<PERSON>ble\PluginManagement\Abstracts\PluginOperationAbstract;
use <PERSON><PERSON><PERSON>\Setting\Facades\Setting;

class Plugin extends PluginOperationAbstract
{
    public static function activate(): void
    {
        // Set default settings when plugin is activated
        $defaultSettings = [
            'payment_nagorikpay_name' => 'NagorikPay',
            'payment_nagorikpay_description' => 'Pay securely with NagorikPay - supports bKash, Nagad, Rocket and other popular payment methods in Bangladesh',
            'payment_nagorikpay_status' => '0', // Disabled by default
            'payment_nagorikpay_mode' => 'sandbox',
        ];

        foreach ($defaultSettings as $key => $value) {
            if (!Setting::get($key)) {
                Setting::set($key, $value);
            }
        }

        Setting::save();
    }

    public static function activated(): void
    {
        // Plugin has been successfully activated
        // You can add any post-activation logic here
    }

    public static function deactivate(): void
    {
        // Plugin is being deactivated
        // You can add cleanup logic here if needed
    }

    public static function remove(): void
    {
        // Plugin is being removed/uninstalled
        // Clean up settings
        $settingsToRemove = [
            'payment_nagorikpay_name',
            'payment_nagorikpay_description',
            'payment_nagorikpay_status',
            'payment_nagorikpay_mode',
            'payment_nagorikpay_api_key',
        ];

        Setting::delete($settingsToRemove);
    }
}
