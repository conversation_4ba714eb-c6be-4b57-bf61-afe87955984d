<?php

namespace Bo<PERSON>ble\Ecommerce\Http\Controllers;

use Bo<PERSON>ble\DataSynchronize\Exporter\Exporter;
use <PERSON><PERSON>ble\DataSynchronize\Http\Controllers\ExportController;
use Bo<PERSON>ble\Ecommerce\Exporters\ProductInventoryExporter;

class ExportProductInventoryController extends ExportController
{
    protected function allowsSelectColumns(): bool
    {
        return false;
    }

    protected function getExporter(): Exporter
    {
        return ProductInventoryExporter::make();
    }
}
