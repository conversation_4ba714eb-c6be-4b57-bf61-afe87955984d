[2025-07-08 09:58:45] local.ERROR: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: delete from `activations` where `completed` = 0 and `created_at` < 2025-07-05 09:58:36) - C:\Users\<USER>\Desktop\uu\vendor\laravel\framework\src\Illuminate\Database\Connection.php:829  
[2025-07-08 10:00:27] local.ERROR: Unable to locate a class or view for component [plugins-ecommerce::fronts.ajax-search.input]. (View: C:\Users\<USER>\Desktop\uu\platform\themes\farmart\partials\header.blade.php) {"exception":"[object] (Illuminate\\View\\ViewException(code: 0): Unable to locate a class or view for component [plugins-ecommerce::fronts.ajax-search.input]. (View: C:\\Users\\<USER>\\Desktop\\uu\\platform\\themes\\farmart\\partials\\header.blade.php) at C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php:311)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(60): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(InvalidArgumentException), 1)
#1 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#2 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#3 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#4 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Botble\\Shortcode\\View\\View->renderContents()
#5 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\theme\\src\\Theme.php(792): Illuminate\\View\\View->render()
#6 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\theme\\src\\Http\\Controllers\\PublicController.php(38): Botble\\Theme\\Theme->render()
#7 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Botble\\Theme\\Http\\Controllers\\PublicController->getIndex()
#8 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('getIndex', Array)
#9 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Botble\\Theme\\Http\\Controllers\\PublicController), 'getIndex')
#10 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#11 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#12 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\CoreMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Botble\\Base\\Http\\Middleware\\CoreMiddleware->Botble\\Base\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\EnsureLicenseHasBeenActivated.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Botble\\Base\\Http\\Middleware\\EnsureLicenseHasBeenActivated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\CoreMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Botble\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Botble\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Botble\\Base\\Http\\Middleware\\AdminLocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Botble\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Botble\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#45 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\platform\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Botble\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#66 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Desktop\\uu\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#68 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#69 {main}

[previous exception] [object] (InvalidArgumentException(code: 0): Unable to locate a class or view for component [plugins-ecommerce::fronts.ajax-search.input]. at C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php:311)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php(235): Illuminate\\View\\Compilers\\ComponentTagCompiler->componentClass('plugins-ecommer...')
#1 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php(220): Illuminate\\View\\Compilers\\ComponentTagCompiler->componentString('plugins-ecommer...', Array)
#2 [internal function]: Illuminate\\View\\Compilers\\ComponentTagCompiler->Illuminate\\View\\Compilers\\{closure}(Array)
#3 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php(215): preg_replace_callback('/\\n            <...', Object(Closure), '<!DOCTYPE html>...')
#4 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php(89): Illuminate\\View\\Compilers\\ComponentTagCompiler->compileSelfClosingTags('<!DOCTYPE html>...')
#5 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php(76): Illuminate\\View\\Compilers\\ComponentTagCompiler->compileTags('<!DOCTYPE html>...')
#6 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\BladeCompiler.php(438): Illuminate\\View\\Compilers\\ComponentTagCompiler->compile('<!DOCTYPE html>...')
#7 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\BladeCompiler.php(270): Illuminate\\View\\Compilers\\BladeCompiler->compileComponentTags('<!DOCTYPE html>...')
#8 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\BladeCompiler.php(184): Illuminate\\View\\Compilers\\BladeCompiler->compileString('<!DOCTYPE html>...')
#9 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(64): Illuminate\\View\\Compilers\\BladeCompiler->compile('C:\\\\Users\\\\<USER>\\\\...')
#10 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#11 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#12 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Botble\\Shortcode\\View\\View->renderContents()
#13 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\theme\\src\\Theme.php(497): Illuminate\\View\\View->render()
#14 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\theme\\src\\Theme.php(483): Botble\\Theme\\Theme->loadPartial('header', 'theme.farmart::...', Array)
#15 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Botble\\Theme\\Theme->partial('header')
#16 C:\\Users\\<USER>\\Desktop\\uu\\storage\\framework\\views\\d50ebd0cf11537a7a81713ccd936f7bd.php(1): Illuminate\\Support\\Facades\\Facade::__callStatic('partial', Array)
#17 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#18 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#19 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#20 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#21 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#22 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#23 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Botble\\Shortcode\\View\\View->renderContents()
#24 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\theme\\src\\Theme.php(792): Illuminate\\View\\View->render()
#25 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\theme\\src\\Http\\Controllers\\PublicController.php(38): Botble\\Theme\\Theme->render()
#26 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Botble\\Theme\\Http\\Controllers\\PublicController->getIndex()
#27 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('getIndex', Array)
#28 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Botble\\Theme\\Http\\Controllers\\PublicController), 'getIndex')
#29 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#30 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#31 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\CoreMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Botble\\Base\\Http\\Middleware\\CoreMiddleware->Botble\\Base\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\EnsureLicenseHasBeenActivated.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Botble\\Base\\Http\\Middleware\\EnsureLicenseHasBeenActivated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\CoreMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Botble\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Botble\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Botble\\Base\\Http\\Middleware\\AdminLocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Botble\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Botble\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#55 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#64 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#66 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\platform\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Botble\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#78 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#80 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#82 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#83 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#84 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#85 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#86 C:\\Users\\<USER>\\Desktop\\uu\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#87 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#88 {main}
"} 
[2025-07-08 10:40:16] local.ERROR: Maximum execution time of 300 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 300 seconds exceeded at C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\ServeCommand.php:116)
[stacktrace]
#0 {main}
"} 
