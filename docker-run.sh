#!/bin/bash

echo "Starting Laravel application with Docker..."

echo ""
echo "Building and starting containers..."
docker-compose up -d --build

echo ""
echo "Waiting for containers to be ready..."
sleep 10

echo ""
echo "Installing/updating composer dependencies..."
docker-compose exec app composer install

echo ""
echo "Setting up application..."
docker-compose exec app php artisan key:generate
docker-compose exec app php artisan config:cache
docker-compose exec app php artisan route:cache

echo ""
echo "Setting permissions..."
docker-compose exec app chown -R www-data:www-data /var/www/html/storage
docker-compose exec app chown -R www-data:www-data /var/www/html/bootstrap/cache

echo ""
echo "Running database migrations..."
docker-compose exec app php artisan migrate --force

echo ""
echo "========================================"
echo "Laravel application is now running!"
echo "========================================"
echo ""
echo "Application: http://localhost:8000"
echo "phpMyAdmin: http://localhost:8099"
echo ""
echo "Database connection:"
echo "Host: localhost"
echo "Port: 4409"
echo "Database: naru"
echo "Username: root"
echo "Password: (empty)"
echo ""
echo "To stop the application: docker-compose down"
echo "To view logs: docker-compose logs -f"
echo "========================================"
