<?php

namespace Bo<PERSON>ble\NagorikPay\Services;

use Botble\Payment\Enums\PaymentStatusEnum;
use Botble\Payment\Models\Payment;
use Exception;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class NagorikPayService
{
    protected string $apiUrl = 'https://secure-pay.nagorikpay.com/api';
    protected string $apiKey;
    protected bool $testMode;

    public function __construct()
    {
        $this->apiKey = get_payment_setting('api_key', NAGORIKPAY_PAYMENT_METHOD_NAME, '');
        $this->testMode = get_payment_setting('mode', NAGORIKPAY_PAYMENT_METHOD_NAME, 'sandbox') !== 'live';
    }

    protected function validateConfiguration(): bool
    {
        return !empty($this->apiKey);
    }

    public function supportedCurrencyCodes(): array
    {
        return ['BDT', 'USD'];
    }

    public function makePayment(array $data): string|array
    {
        try {
            if (!$this->validateConfiguration()) {
                return [
                    'error' => true,
                    'message' => 'NagorikPay is not properly configured. Please check API key settings.'
                ];
            }

            $paymentData = $this->preparePaymentData($data);

            $response = Http::timeout(30)->withHeaders([
                'API-KEY' => $this->apiKey,
                'Content-Type' => 'application/json',
            ])->post($this->apiUrl . '/payment/create', $paymentData);

            $responseData = $response->json();

            if ($response->successful() && isset($responseData['status']) && $responseData['status']) {
                // Store payment record
                $this->storePayment($data, $responseData);

                // Return the payment URL directly (like bKash does)
                return $responseData['payment_url'];
            }

            return [
                'error' => true,
                'message' => $responseData['message'] ?? 'Payment initiation failed'
            ];

        } catch (Exception $e) {
            Log::error('NagorikPay payment error: ' . $e->getMessage());

            return [
                'error' => true,
                'message' => 'Payment service temporarily unavailable: ' . $e->getMessage()
            ];
        }
    }

    public function verifyPayment(string $transactionId): array
    {
        try {
            $response = Http::withHeaders([
                'API-KEY' => $this->apiKey,
                'Content-Type' => 'application/json',
            ])->post($this->apiUrl . '/payment/verify', [
                'transaction_id' => $transactionId
            ]);

            $responseData = $response->json();

            if ($response->successful() && isset($responseData['status'])) {
                return [
                    'status' => true,
                    'data' => $responseData
                ];
            }

            return [
                'status' => false,
                'message' => $responseData['message'] ?? 'Verification failed'
            ];

        } catch (Exception $e) {
            Log::error('NagorikPay verification error: ' . $e->getMessage());
            
            return [
                'status' => false,
                'message' => 'Verification service temporarily unavailable'
            ];
        }
    }

    protected function preparePaymentData(array $data): array
    {
        $successUrl = route('payments.nagorikpay.success');
        $cancelUrl = route('payments.nagorikpay.cancel');
        $webhookUrl = route('payments.nagorikpay.webhook');

        return [
            'success_url' => $successUrl,
            'cancel_url' => $cancelUrl,
            'webhook_url' => $webhookUrl,
            'amount' => number_format($data['amount'], 2, '.', ''),
            'metadata' => [
                'order_id' => $data['order_id'] ?? null,
                'customer_id' => $data['customer_id'] ?? null,
                'customer_name' => $data['customer_name'] ?? null,
                'customer_email' => $data['customer_email'] ?? null,
                'customer_phone' => $data['customer_phone'] ?? null,
            ]
        ];
    }

    protected function storePayment(array $data, array $responseData): void
    {
        $payment = new Payment();
        $payment->currency = $data['currency'];
        $payment->user_id = $data['customer_id'] ?? null;
        $payment->charge_id = $responseData['payment_url'] ?? null;
        $payment->payment_channel = NAGORIKPAY_PAYMENT_METHOD_NAME;
        $payment->description = $data['description'] ?? null;
        $payment->amount = $data['amount'];
        $payment->order_id = $data['order_id'] ?? null;
        $payment->status = PaymentStatusEnum::PENDING;
        $payment->payment_type = 'confirm';
        $payment->customer_id = $data['customer_id'] ?? null;
        $payment->customer_type = $data['customer_type'] ?? null;
        $payment->save();
    }

    public function updatePaymentStatus(string $transactionId, string $status, array $additionalData = []): bool
    {
        try {
            $payment = Payment::where('charge_id', 'like', '%' . $transactionId . '%')->first();

            if (!$payment) {
                return false;
            }

            switch (strtoupper($status)) {
                case 'COMPLETED':
                    $payment->status = PaymentStatusEnum::COMPLETED;
                    break;
                case 'PENDING':
                    $payment->status = PaymentStatusEnum::PENDING;
                    break;
                case 'ERROR':
                case 'FAILED':
                    $payment->status = PaymentStatusEnum::FAILED;
                    break;
                default:
                    $payment->status = PaymentStatusEnum::PENDING;
            }

            if (isset($additionalData['payment_method'])) {
                $payment->payment_channel = NAGORIKPAY_PAYMENT_METHOD_NAME . '_' . $additionalData['payment_method'];
            }

            $payment->save();
            return true;

        } catch (Exception $e) {
            Log::error('NagorikPay payment status update error: ' . $e->getMessage());
            return false;
        }
    }
}
