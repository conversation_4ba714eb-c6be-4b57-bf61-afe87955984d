<?php

namespace Botble\NagorikPay\Services;

use Botble\Payment\Enums\PaymentStatusEnum;
use Botble\Payment\Models\Payment;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class NagorikPayService
{
    protected string $apiKey;
    protected string $apiUrl;
    protected float $currencyRate;

    public function __construct()
    {
        $this->apiKey = get_payment_setting('api_key', NAGORIKPAY_PAYMENT_METHOD_NAME, '');
        $this->apiUrl = NAGORIKPAY_API_URL;
        $this->currencyRate = (float) get_payment_setting('currency_rate', NAGORIKPAY_PAYMENT_METHOD_NAME, 85);
    }

    public function supportedCurrencyCodes(): array
    {
        return ['BDT', 'USD'];
    }

    public function makePayment(array $data): string|array
    {
        try {
            if (!$this->validateConfiguration()) {
                return [
                    'error' => true,
                    'message' => 'NagorikPay is not properly configured. Please check API key settings.'
                ];
            }

            $paymentData = $this->preparePaymentData($data);
            
            $response = Http::timeout(30)->withHeaders([
                'API-KEY' => $this->apiKey,
                'Content-Type' => 'application/json',
            ])->post($this->apiUrl . '/payment/create', $paymentData);

            $responseData = $response->json();

            if ($response->successful() && isset($responseData['status']) && $responseData['status']) {
                // Store payment record
                $this->storePayment($data, $responseData);
                
                // Return the payment URL directly (like WHMCS does)
                return $responseData['payment_url'];
            }

            return [
                'error' => true,
                'message' => $responseData['message'] ?? 'Payment initiation failed'
            ];

        } catch (Exception $e) {
            Log::error('NagorikPay payment error: ' . $e->getMessage());
            
            return [
                'error' => true,
                'message' => 'Payment service temporarily unavailable: ' . $e->getMessage()
            ];
        }
    }

    protected function validateConfiguration(): bool
    {
        return !empty($this->apiKey);
    }

    protected function preparePaymentData(array $data): array
    {
        // Convert amount based on currency (like WHMCS plugin)
        $amount = $data['amount'];
        if (isset($data['currency']) && strtoupper($data['currency']) === 'USD') {
            $amount = $amount * $this->currencyRate;
        }

        // Get customer details
        $customerName = $data['customer_name'] ?? 'Customer';
        $customerEmail = $data['customer_email'] ?? '<EMAIL>';

        // Generate URLs
        $baseUrl = url('/');
        $orderId = $data['order_id'] ?? uniqid('order_');
        
        return [
            'cus_name' => $customerName,
            'cus_email' => $customerEmail,
            'amount' => $amount,
            'webhook_url' => route('payments.nagorikpay.webhook', [
                'api' => $this->apiKey,
                'order' => $orderId
            ]),
            'success_url' => route('payments.nagorikpay.success', ['order' => $orderId]),
            'cancel_url' => route('payments.nagorikpay.cancel', ['order' => $orderId]),
        ];
    }

    protected function storePayment(array $data, array $response): void
    {
        try {
            $payment = new Payment();
            $payment->amount = $data['amount'];
            $payment->currency = $data['currency'] ?? get_application_currency()->title;
            $payment->payment_channel = NAGORIKPAY_PAYMENT_METHOD_NAME;
            $payment->status = PaymentStatusEnum::PENDING;
            $payment->customer_id = $data['customer_id'] ?? null;
            $payment->customer_type = $data['customer_type'] ?? null;
            $payment->order_id = $data['order_id'] ?? null;
            $payment->charge_id = $response['transaction_id'] ?? null;
            $payment->save();
        } catch (Exception $e) {
            Log::error('Failed to store payment: ' . $e->getMessage());
        }
    }

    public function verifyPayment(string $transactionId): array
    {
        try {
            $response = Http::timeout(30)->withHeaders([
                'API-KEY' => $this->apiKey,
                'Content-Type' => 'application/json',
            ])->post($this->apiUrl . '/payment/verify', [
                'transaction_id' => $transactionId
            ]);

            $responseData = $response->json();

            if ($response->successful() && isset($responseData['status'])) {
                return [
                    'success' => $responseData['status'] === 'COMPLETED',
                    'status' => $responseData['status'],
                    'data' => $responseData
                ];
            }

            return [
                'success' => false,
                'message' => 'Verification failed'
            ];

        } catch (Exception $e) {
            Log::error('NagorikPay verification error: ' . $e->getMessage());
            
            return [
                'success' => false,
                'message' => 'Verification service unavailable'
            ];
        }
    }
}
