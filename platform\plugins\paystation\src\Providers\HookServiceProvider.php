<?php

namespace Bo<PERSON><PERSON>\PayStation\Providers;

use <PERSON><PERSON><PERSON>\Base\Facades\Html;
use <PERSON><PERSON>ble\Payment\Enums\PaymentMethodEnum;
use Bo<PERSON><PERSON>\Payment\Facades\PaymentMethods;
use Bo<PERSON>ble\PayStation\Forms\PayStationPaymentMethodForm;
use Botble\PayStation\Services\PayStationService;
use Illuminate\Http\Request;
use Illuminate\Support\ServiceProvider;

class HookServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        // Only register essential hooks to improve performance
        add_filter(PAYMENT_FILTER_ADDITIONAL_PAYMENT_METHODS, [$this, 'registerPayStationMethod'], 16, 2);
        add_filter(PAYMENT_METHODS_SETTINGS_PAGE, [$this, 'addPaymentSettings'], 16);

        // Only add checkout hook when actually needed
        if (request()->is('checkout*') || request()->is('payment*')) {
            add_filter(PAYMENT_FILTER_AFTER_POST_CHECKOUT, [$this, 'checkoutWithPayStation'], 16, 2);
        }

        add_filter(PAYMENT_FILTER_GET_SERVICE_CLASS, [$this, 'getServiceClass'], 16, 2);

        // Register PayStation in payment method enum
        add_filter(BASE_FILTER_ENUM_ARRAY, [$this, 'addPaymentMethodEnum'], 16, 2);
        add_filter(BASE_FILTER_ENUM_LABEL, [$this, 'addPaymentMethodLabel'], 16, 2);
        add_filter(BASE_FILTER_ENUM_HTML, [$this, 'addPaymentMethodHtml'], 16, 2);
    }

    public function addPaymentSettings(?string $settings): string
    {
        return $settings . PayStationPaymentMethodForm::create()->renderForm();
    }

    public function registerPayStationMethod(?string $html, array $data): string
    {
        PaymentMethods::method(PAYSTATION_PAYMENT_METHOD_NAME, [
            'html' => view('plugins/paystation::methods', $data)->render(),
        ]);

        return $html;
    }

    public function checkoutWithPayStation(array $data, Request $request): array
    {
        if ($data['type'] !== PAYSTATION_PAYMENT_METHOD_NAME) {
            return $data;
        }

        try {
            $payStationService = $this->app->make(PayStationService::class);
            $checkoutUrl = $payStationService->makePayment($data);

            if (is_array($checkoutUrl) && isset($checkoutUrl['error'])) {
                $data['error'] = true;
                $data['message'] = $checkoutUrl['message'];
            } else {
                $data['checkoutUrl'] = $checkoutUrl;
            }
        } catch (\Exception $e) {
            $data['error'] = true;
            $data['message'] = 'Payment service temporarily unavailable.';
        }

        return $data;
    }

    public function getServiceClass($data, $value)
    {
        if ($value == PAYSTATION_PAYMENT_METHOD_NAME) {
            return PayStationService::class;
        }
        return $data;
    }

    public function addPaymentMethodEnum($values, $class)
    {
        if ($class == PaymentMethodEnum::class) {
            $values['PAYSTATION'] = PAYSTATION_PAYMENT_METHOD_NAME;
        }
        return $values;
    }

    public function addPaymentMethodLabel($value, $class)
    {
        if ($class == PaymentMethodEnum::class && $value == PAYSTATION_PAYMENT_METHOD_NAME) {
            return 'PayStation';
        }
        return $value;
    }

    public function addPaymentMethodHtml($value, $class)
    {
        if ($class == PaymentMethodEnum::class && $value == PAYSTATION_PAYMENT_METHOD_NAME) {
            return Html::tag(
                'span',
                PaymentMethodEnum::getLabel($value),
                ['class' => 'label-success status-label']
            )->toHtml();
        }
        return $value;
    }
}
