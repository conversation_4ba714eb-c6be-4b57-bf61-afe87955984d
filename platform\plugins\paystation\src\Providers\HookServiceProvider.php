<?php

namespace Bo<PERSON>ble\PayStation\Providers;

use Bo<PERSON>ble\Payment\Facades\PaymentMethods;
use Bo<PERSON>ble\PayStation\Forms\PayStationPaymentMethodForm;
use Botble\PayStation\Services\PayStationService;
use Illuminate\Http\Request;
use Illuminate\Support\ServiceProvider;

class HookServiceProvider extends ServiceProvider
{
    private static $registered = false;

    public function boot(): void
    {
        // Prevent multiple registrations
        if (self::$registered) {
            return;
        }
        self::$registered = true;

        // Register payment method
        add_filter(PAYMENT_FILTER_ADDITIONAL_PAYMENT_METHODS, [$this, 'registerPayStationMethod'], 16, 2);
        add_filter(PAYMENT_METHODS_SETTINGS_PAGE, [$this, 'addPaymentSettings'], 16);
        
        // Only add checkout hook if not in admin
        if (!is_admin()) {
            add_filter(PAYMENT_FILTER_AFTER_POST_CHECKOUT, [$this, 'checkoutWithPayStation'], 16, 2);
        }
    }

    public function registerPayStationMethod(?string $html, array $data): string
    {
        try {
            if (get_payment_setting('status', PAYSTATION_PAYMENT_METHOD_NAME) == 1) {
                PaymentMethods::method(PAYSTATION_PAYMENT_METHOD_NAME, [
                    'html' => view('plugins/paystation::methods', $data)->render(),
                ]);
            }
        } catch (\Exception $e) {
            \Log::error('PayStation method registration error: ' . $e->getMessage());
        }

        return $html;
    }

    public function addPaymentSettings(?string $settings): string
    {
        try {
            return $settings . PayStationPaymentMethodForm::create()->renderForm();
        } catch (\Exception $e) {
            \Log::error('PayStation settings form error: ' . $e->getMessage());
            return $settings;
        }
    }

    public function checkoutWithPayStation(array $data, Request $request): array
    {
        // Prevent recursive calls
        static $processing = false;
        if ($processing) {
            return $data;
        }

        try {
            if ($request->input('payment_method') === PAYSTATION_PAYMENT_METHOD_NAME && $request->isMethod('POST')) {
                $processing = true;
                
                \Log::info('PayStation: Processing checkout', [
                    'payment_method' => $request->input('payment_method'),
                    'request_method' => $request->method(),
                    'url' => $request->url()
                ]);

                $currentCurrency = get_application_currency();
                $payStationService = $this->app->make(PayStationService::class);

                // Check currency support
                $supportedCurrencies = $payStationService->supportedCurrencyCodes();
                $currency = strtoupper($currentCurrency->title);

                if (!in_array($currency, $supportedCurrencies)) {
                    \Log::warning('PayStation: Unsupported currency', ['currency' => $currency]);
                    $data['error'] = true;
                    $data['message'] = "PayStation doesn't support {$currency}. Supported currencies: " . implode(', ', $supportedCurrencies);
                    $processing = false;
                    return $data;
                }

                // Get payment data
                $paymentData = apply_filters(PAYMENT_FILTER_PAYMENT_DATA, [], $request);
                
                if (empty($paymentData)) {
                    \Log::error('PayStation: Empty payment data');
                    $data['error'] = true;
                    $data['message'] = 'Payment data is missing. Please try again.';
                    $processing = false;
                    return $data;
                }

                \Log::info('PayStation: Payment data prepared', $paymentData);

                // Create payment
                $checkoutUrl = $payStationService->makePayment($paymentData);

                if (is_array($checkoutUrl) && isset($checkoutUrl['error']) && $checkoutUrl['error']) {
                    \Log::error('PayStation: Payment creation failed', $checkoutUrl);
                    $data['error'] = true;
                    $data['message'] = $checkoutUrl['message'] ?? 'Something went wrong. Please try again later.';
                } else {
                    if (is_string($checkoutUrl) && !empty($checkoutUrl)) {
                        \Log::info('PayStation: Redirecting to payment URL', ['url' => $checkoutUrl]);
                        $data['checkoutUrl'] = $checkoutUrl;
                    } else {
                        \Log::error('PayStation: Invalid checkout URL', ['url' => $checkoutUrl]);
                        $data['error'] = true;
                        $data['message'] = 'Invalid payment URL received. Please try again.';
                    }
                }

                $processing = false;
                return $data;
            }
        } catch (\Exception $e) {
            $processing = false;
            \Log::error('PayStation checkout error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            $data['error'] = true;
            $data['message'] = 'Payment service temporarily unavailable. Please try again.';
        }

        return $data;
    }
}
