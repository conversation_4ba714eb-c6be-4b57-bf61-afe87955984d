<?php

namespace Botble\PayStation\Services;

use Botble\Payment\Enums\PaymentStatusEnum;
use Bo<PERSON>ble\Payment\Supports\PaymentHelper;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class PayStationService
{
    protected string $merchantId;
    protected string $password;
    protected string $apiUrl;
    protected bool $isLive;

    public function __construct()
    {
        $this->merchantId = get_payment_setting('merchant_id', PAYSTATION_PAYMENT_METHOD_NAME, '');
        $this->password = get_payment_setting('password', PAYSTATION_PAYMENT_METHOD_NAME, '');
        
        $environment = get_payment_setting('environment', PAYSTATION_PAYMENT_METHOD_NAME, 'sandbox');
        $this->isLive = $environment === 'live';
        
        $this->apiUrl = $this->isLive 
            ? 'https://api.paystation.com.bd' 
            : 'https://api-sandbox.paystation.com.bd';
    }

    public function makePayment(array $data): string|array
    {
        try {
            if (!$this->validateConfiguration()) {
                return [
                    'error' => true,
                    'message' => 'PayStation is not properly configured. Please check merchant credentials.'
                ];
            }

            $paymentData = $this->preparePaymentData($data);
            
            Log::info('PayStation: Creating payment', $paymentData);

            $response = Http::timeout(30)->post($this->apiUrl . '/initiate-payment', $paymentData);

            $responseData = $response->json();

            Log::info('PayStation API Response', [
                'status_code' => $response->status(),
                'response' => $responseData
            ]);

            if ($response->successful() && isset($responseData['status_code']) && $responseData['status_code'] === '200') {
                Log::info('PayStation: Payment created successfully', [
                    'payment_url' => $responseData['payment_url']
                ]);
                
                return $responseData['payment_url'];
            }

            Log::error('PayStation: Payment creation failed', [
                'response' => $responseData
            ]);

            return [
                'error' => true,
                'message' => $responseData['message'] ?? 'Payment initiation failed'
            ];

        } catch (Exception $e) {
            Log::error('PayStation payment error: ' . $e->getMessage());
            
            return [
                'error' => true,
                'message' => 'Payment service temporarily unavailable. Please try again.'
            ];
        }
    }

    protected function validateConfiguration(): bool
    {
        return !empty($this->merchantId) && !empty($this->password);
    }

    protected function preparePaymentData(array $data): array
    {
        $amount = floatval($data['amount'] ?? 0);
        $currency = $data['currency'] ?? 'BDT';
        
        // Convert USD to BDT if needed
        if (strtoupper($currency) === 'USD') {
            $rate = get_payment_setting('currency_rate', PAYSTATION_PAYMENT_METHOD_NAME, 110);
            $amount = $amount * $rate;
        }

        $customerName = $data['customer_name'] ?? $data['name'] ?? 'Customer';
        $customerEmail = $data['customer_email'] ?? $data['email'] ?? '<EMAIL>';
        $customerPhone = $data['customer_phone'] ?? $data['phone'] ?? '01700000000';
        $customerAddress = $data['customer_address'] ?? $data['address'] ?? 'Dhaka, Bangladesh';

        $invoiceNumber = $data['order_id'] ?? $data['charge_id'] ?? uniqid('paystation_');

        $paymentData = [
            'merchantId' => $this->merchantId,
            'password' => $this->password,
            'invoice_number' => $invoiceNumber,
            'currency' => 'BDT',
            'payment_amount' => (int)$amount,
            'pay_with_charge' => 1,
            'reference' => 'Order #' . $invoiceNumber,
            'cust_name' => $customerName,
            'cust_phone' => $customerPhone,
            'cust_email' => $customerEmail,
            'cust_address' => $customerAddress,
            'callback_url' => route('payments.paystation.callback'),
            'webhook_url' => route('payments.paystation.webhook'),
            'checkout_items' => json_encode([
                'order_id' => $invoiceNumber,
                'amount' => $amount,
                'currency' => $currency
            ]),
            'opt_a' => $data['description'] ?? 'Payment via PayStation',
            'opt_b' => url('/'),
            'opt_c' => $invoiceNumber
        ];

        // Add payment method if specified
        if (isset($data['payment_method']) && !empty($data['payment_method'])) {
            $paymentData['payment_method'] = $data['payment_method'];
        }

        return $paymentData;
    }

    public function getTransactionStatus(string $invoiceNumber): array
    {
        try {
            $response = Http::withHeaders([
                'merchantId' => $this->merchantId
            ])->post($this->apiUrl . '/transaction-status', [
                'invoice_number' => $invoiceNumber
            ]);

            $responseData = $response->json();

            if ($response->successful() && isset($responseData['status_code']) && $responseData['status_code'] === '200') {
                return [
                    'success' => true,
                    'data' => $responseData['data']
                ];
            }

            return [
                'success' => false,
                'message' => $responseData['message'] ?? 'Transaction status check failed'
            ];

        } catch (Exception $e) {
            Log::error('PayStation transaction status error: ' . $e->getMessage());
            
            return [
                'success' => false,
                'message' => 'Unable to check transaction status'
            ];
        }
    }

    public function supportedCurrencyCodes(): array
    {
        return ['BDT', 'USD'];
    }
}
